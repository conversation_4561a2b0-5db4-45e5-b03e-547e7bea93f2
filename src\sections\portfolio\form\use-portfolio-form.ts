import { useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { useParams, useNavigate } from "react-router-dom";
import { usePortfolioApi } from "../../../services/api/use-portfolio-api";
import type { Portfolio, PortfolioFormData } from "../types/types";

// --- The Hook ---
export function usePortfolioForm() {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  const navigate = useNavigate();

  const { useGetPortfolio, useCreatePortfolio, useUpdatePortfolio } =
    usePortfolioApi();

  // Get portfolio data for editing
  const { data: portfolioResponse, isLoading } = useGetPortfolio(
    isEditMode ? parseInt(id!) : 0
  );

  // Create and update mutations
  const { mutate: createPortfolio, isPending: isCreating } = useCreatePortfolio(
    () => {
      navigate("/dashboard/portfolios");
    }
  );

  const { mutate: updatePortfolio, isPending: isUpdating } = useUpdatePortfolio(
    parseInt(id!),
    () => {
      navigate("/dashboard/portfolios");
    }
  );

  const formMethods = useForm<PortfolioFormData>({
    defaultValues: {
      title: "",
      descriptions: [{ description: "", order: "1", images: [] }],
    },
  });

  useEffect(() => {
    if (isEditMode && portfolioResponse?.data) {
      const portfolio = portfolioResponse.data;
      formMethods.reset({
        title: portfolio.title,
        // Map portfolio descriptions for the form, handling images separately
        descriptions: portfolio.descriptions.map((desc) => ({
          description: desc.description,
          order: desc.order.toString(),
          images: [], // Images must be re-uploaded
        })),
      });
    }
  }, [portfolioResponse, isEditMode, formMethods]);

  const { fields } = useFieldArray({
    control: formMethods.control,
    name: "descriptions",
  });

  const onSubmit = (data: PortfolioFormData) => {
    const formData = new FormData();
    formData.append("title", data.title);

    data.descriptions.forEach((desc, descIndex) => {
      formData.append(
        `descriptions[${descIndex}][description]`,
        desc.description
      );
      formData.append(`descriptions[${descIndex}][order]`, desc.order);

      // Handle images with the correct key format: descriptions[index][images][]
      if (desc.images && desc.images.length > 0) {
        for (let i = 0; i < desc.images.length; i++) {
          formData.append(
            `descriptions[${descIndex}][images][]`,
            desc.images[i]
          );
        }
      }
    });

    if (isEditMode) {
      formData.append("_method", "patch");
      updatePortfolio(formData);
    } else {
      createPortfolio(formData);
    }
  };

  return {
    formMethods,
    onSubmit,
    isLoading,
    isSubmitting: isCreating || isUpdating,
    isEditMode,
    existingPortfolio: portfolioResponse?.data,
  };
}
