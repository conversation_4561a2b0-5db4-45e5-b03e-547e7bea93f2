import { useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Portfolio, PortfolioFormData } from '../types/types';
import { mockPortfolios } from '../../../mock-data/mockData'; // Adjust path if needed

// --- Data Fetching and Mutations (Moved from page files) ---
const fetchPortfolioById = async (id: string): Promise<Portfolio> => {
  console.log(`Simulating fetch for portfolio with id: ${id}`);
  await new Promise((resolve) => setTimeout(resolve, 500));
  const portfolio = mockPortfolios.find((p) => p.id === id);
  if (!portfolio) throw new Error("Portfolio not found");
  return portfolio;
};

const updatePortfolio = async ({ id, data }: { id: string; data: FormData }) => {
  console.log(`Simulating update for portfolio with id: ${id}`);
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return { success: true };
};

const addPortfolio = async (data: FormData) => {
  console.log('Simulating adding new portfolio');
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return { success: true };
}

// --- The Hook ---
export function usePortfolioForm() {
    const { id } = useParams<{ id: string }>();
    const isEditMode = !!id;
    const navigate = useNavigate();
    const queryClient = useQueryClient();

    const { data: initialData, isLoading } = useQuery({
        queryKey: ["portfolios", id],
        queryFn: () => fetchPortfolioById(id!),
        enabled: isEditMode,
    });

    const { mutate: update, isPending: isUpdating } = useMutation({ mutationFn: updatePortfolio });
    const { mutate: add, isPending: isAdding } = useMutation({ mutationFn: addPortfolio });

    const formMethods = useForm<PortfolioFormData>({
        defaultValues: {
            name: '',
            items: [ { text: '', images: new DataTransfer().files }, { text: '', images: new DataTransfer().files }, { text: '', images: new DataTransfer().files } ],
        },
    });

    useEffect(() => {
        if (isEditMode && initialData) {
            formMethods.reset({
                name: initialData.name,
                // Map initialData items for the form, handling images separately
                items: initialData.items.map(item => ({
                    text: item.text,
                    images: new DataTransfer().files, // Images must be re-uploaded
                })),
            });
        }
    }, [initialData, isEditMode, formMethods]);
    
    const { fields } = useFieldArray({ control: formMethods.control, name: "items" });

    const onSubmit = (data: PortfolioFormData) => {
        const formData = new FormData();
        formData.append('name', data.name);
        data.items.forEach((item, itemIndex) => {
            formData.append(`items[${itemIndex}][text]`, item.text);
            if (item.images && item.images.length > 0) {
                for (let i = 0; i < item.images.length; i++) {
                    formData.append(`item_images_${itemIndex}`, item.images[i]);
                }
            }
        });
        
        const onMutationSuccess = () => {
            alert(`Portfolio ${isEditMode ? 'updated' : 'added'} successfully!`);
            queryClient.invalidateQueries({ queryKey: ["portfolios"] });
            navigate("/dashboard/portfolios");
        };

        if(isEditMode) {
            update({ id: id!, data: formData }, { onSuccess: onMutationSuccess });
        } else {
            add(formData, { onSuccess: onMutationSuccess });
        }
    };

    return {
        formMethods,
        fields,
        onSubmit,
        isLoading,
        isSubmitting: isAdding || isUpdating,
        isEditMode
    };
}