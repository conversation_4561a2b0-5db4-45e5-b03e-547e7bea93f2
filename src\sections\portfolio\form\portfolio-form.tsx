import { useState } from "react";
import { useFormContext, useFieldArray } from "react-hook-form";
import type { PortfolioFormData, Portfolio, Media } from "../types/types";

interface PortfolioFormProps {
  existingPortfolio?: Portfolio;
}

export function PortfolioForm({ existingPortfolio }: PortfolioFormProps) {
  const [selectedImage, setSelectedImage] = useState<Media | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const {
    register,
    control,
    formState: { errors },
  } = useFormContext<PortfolioFormData>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "descriptions",
  });

  const addDescription = () => {
    append({
      description: "",
      order: (fields.length + 1).toString(),
      images: [],
    });
  };

  const removeDescription = (index: number) => {
    remove(index);
  };

  const openImageModal = (media: Media) => {
    setSelectedImage(media);
    setIsModalOpen(true);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-8 bg-white p-8 rounded-lg shadow-md max-w-4xl mx-auto">
      <div>
        <label className="block text-xl font-medium text-secondary-01">
          Portfolio Title
        </label>
        <input
          {...register("title", { required: "Portfolio title is required" })}
          className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01"
          placeholder="Enter portfolio title"
        />
        {errors.title && (
          <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
        )}
      </div>

      {/* Descriptions Section */}
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-secondary-01">
            Descriptions
          </h3>
          <button
            type="button"
            onClick={addDescription}
            className="px-4 py-2 bg-primary-01 text-white rounded-md hover:bg-primary-01/90 focus:outline-none focus:ring-2 focus:ring-primary-01 focus:ring-offset-2"
          >
            + Add Description
          </button>
        </div>

        {fields.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No descriptions added yet. Click "Add Description" to get started.
          </div>
        )}

        {fields.map((field, index) => (
          <fieldset
            key={field.id}
            className="border border-gray-300 p-6 rounded-md relative"
          >
            <legend className="text-lg font-medium text-secondary-01 px-2">
              Description {index + 1}
            </legend>

            {/* Remove button */}
            <button
              type="button"
              onClick={() => removeDescription(index)}
              className="absolute top-4 right-4 text-red-600 hover:text-red-800 focus:outline-none"
              title="Remove this description"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-secondary-01">
                    Order
                  </label>
                  <input
                    type="number"
                    {...register(`descriptions.${index}.order` as const, {
                      required: "Order is required",
                      min: { value: 1, message: "Order must be at least 1" },
                    })}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01"
                    placeholder="1"
                  />
                  {errors.descriptions?.[index]?.order && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.descriptions[index]?.order?.message}
                    </p>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-01">
                  Description
                </label>
                <textarea
                  {...register(`descriptions.${index}.description` as const, {
                    required: "Description is required.",
                  })}
                  rows={4}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01"
                  placeholder="Enter description text"
                />
                {errors.descriptions?.[index]?.description && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.descriptions[index]?.description?.message}
                  </p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-secondary-01">
                  Images (multiple can be selected)
                </label>
                <input
                  type="file"
                  {...register(`descriptions.${index}.images` as const)}
                  multiple
                  accept="image/*"
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-01 file:text-white hover:file:bg-primary-01/90"
                />

                {/* Existing Images Preview */}
                {existingPortfolio &&
                  existingPortfolio.descriptions[index]?.media &&
                  existingPortfolio.descriptions[index].media.length > 0 && (
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-secondary-01 mb-3">
                        Current Images (
                        {existingPortfolio.descriptions[index].media.length})
                      </h4>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                        {existingPortfolio.descriptions[index].media.map(
                          (media) => (
                            <div key={media.id} className="relative group">
                              <div className="aspect-square overflow-hidden rounded-lg border border-gray-200 bg-gray-50">
                                {media.type === "image" ? (
                                  <img
                                    src={media.url}
                                    alt={`Media ${media.id}`}
                                    className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                                    onError={(e) => {
                                      const target =
                                        e.target as HTMLImageElement;
                                      target.style.display = "none";
                                      const parent = target.parentElement;
                                      if (parent) {
                                        parent.innerHTML = `
                                      <div class="w-full h-full flex items-center justify-center text-gray-400 bg-gray-100">
                                        <div class="text-center">
                                          <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                          </svg>
                                          <span class="text-xs">Failed to load</span>
                                        </div>
                                      </div>
                                    `;
                                      }
                                    }}
                                  />
                                ) : (
                                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                                    <svg
                                      className="w-8 h-8"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M7 4V2C7 1.44772 7.44772 1 8 1H16C16.5523 1 17 1.44772 17 2V4M7 4H17M7 4L5 6M17 4L19 6M5 6V20C5 21.1046 5.89543 22 7 22H17C18.1046 22 19 21.1046 19 20V6M5 6H19"
                                      />
                                    </svg>
                                  </div>
                                )}
                              </div>

                              {/* Image overlay with info */}
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 rounded-lg flex items-center justify-center">
                                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                                  <button
                                    type="button"
                                    onClick={() => openImageModal(media)}
                                    className="bg-white text-gray-800 px-3 py-1 rounded-md text-sm font-medium hover:bg-gray-100 transition-colors"
                                  >
                                    Preview
                                  </button>
                                  <button
                                    type="button"
                                    onClick={() =>
                                      window.open(media.url, "_blank")
                                    }
                                    className="bg-primary-01 text-white px-3 py-1 rounded-md text-sm font-medium hover:bg-primary-01/90 transition-colors"
                                  >
                                    Open
                                  </button>
                                </div>
                              </div>

                              {/* Media type badge */}
                              <div className="absolute top-2 left-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                                {media.type}
                              </div>
                            </div>
                          )
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        💡 Upload new images above to add more media to this
                        description
                      </p>
                    </div>
                  )}
              </div>
            </div>
          </fieldset>
        ))}
      </div>

      {/* Image Preview Modal */}
      {isModalOpen && selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full bg-white rounded-lg overflow-hidden">
            {/* Modal Header */}
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                Image Preview
              </h3>
              <button
                type="button"
                onClick={closeImageModal}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-4">
              <div className="flex flex-col items-center">
                {selectedImage.type === "image" ? (
                  <img
                    src={selectedImage.url}
                    alt={`Media ${selectedImage.id}`}
                    className="max-w-full max-h-[70vh] object-contain rounded-lg shadow-lg"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = "none";
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = `
                          <div class="flex items-center justify-center text-gray-400 bg-gray-100 rounded-lg p-8">
                            <div class="text-center">
                              <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                              </svg>
                              <p class="text-lg">Failed to load image</p>
                              <p class="text-sm text-gray-500 mt-2">The image could not be displayed</p>
                            </div>
                          </div>
                        `;
                      }
                    }}
                  />
                ) : (
                  <div className="flex items-center justify-center text-gray-400 bg-gray-100 rounded-lg p-8">
                    <div className="text-center">
                      <svg
                        className="w-16 h-16 mx-auto mb-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 4V2C7 1.44772 7.44772 1 8 1H16C16.5523 1 17 1.44772 17 2V4M7 4H17M7 4L5 6M17 4L19 6M5 6V20C5 21.1046 5.89543 22 7 22H17C18.1046 22 19 21.1046 19 20V6M5 6H19"
                        />
                      </svg>
                      <p className="text-lg">
                        Media Type: {selectedImage.type}
                      </p>
                      <p className="text-sm text-gray-500 mt-2">
                        This media type cannot be previewed
                      </p>
                    </div>
                  </div>
                )}

                {/* Image Info */}
                <div className="mt-4 text-center">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Media ID:</span>{" "}
                    {selectedImage.id}
                  </p>
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Type:</span>{" "}
                    {selectedImage.type}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="mt-6 flex gap-3">
                  <button
                    type="button"
                    onClick={() => window.open(selectedImage.url, "_blank")}
                    className="px-4 py-2 bg-primary-01 text-white rounded-md hover:bg-primary-01/90 transition-colors"
                  >
                    Open in New Tab
                  </button>
                  <button
                    type="button"
                    onClick={closeImageModal}
                    className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={closeImageModal}
          ></div>
        </div>
      )}
    </div>
  );
}
