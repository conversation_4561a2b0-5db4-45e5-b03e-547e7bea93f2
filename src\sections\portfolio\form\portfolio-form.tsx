import { useFormContext } from 'react-hook-form';
import type { PortfolioFormData } from '../types/types';
import type { FieldArrayWithId } from 'react-hook-form';

interface PortfolioFormProps {
  fields: FieldArrayWithId<PortfolioFormData, "items", "id">[];
}

export function PortfolioForm({ fields }: PortfolioFormProps) {
  const { register, formState: { errors } } = useFormContext<PortfolioFormData>();

  return (
    <div className="space-y-8 bg-white p-8 rounded-lg shadow-md max-w-4xl mx-auto">
      <div>
        <label className="block text-xl font-medium text-secondary-01">Portfolio Name</label>
        <input
          {...register('name', { required: 'Portfolio name is required' })}
          className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-prring-primary-01"
        />
        {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
      </div>

      {fields.map((field, index) => (
        <fieldset key={field.id} className="border p-6 rounded-md">
          <legend className="text-lg font-medium text-secondary-01 px-2">Item {index + 1}</legend>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-secondary-01">Text</label>
              <textarea
                {...register(`items.${index}.text` as const, { required: 'Text is required.' })}
                rows={4}
                className="w-full mt-1 input-style"
              />
              {errors.items?.[index]?.text && <p className="text-red-500 text-sm mt-1">{errors.items[index]?.text?.message}</p>}
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-01">Images (multiple can be selected)</label>
              <input
                type="file"
                {...register(`items.${index}.images` as const)}
                multiple
                className="w-full mt-1 file-input"
              />
            </div>
          </div>
        </fieldset>
      ))}
    </div>
  );
}