import { useFormContext } from "react-hook-form";
import type { PortfolioFormData } from "../types/types";
import type { FieldArrayWithId } from "react-hook-form";

interface PortfolioFormProps {
  fields: FieldArrayWithId<PortfolioFormData, "descriptions", "id">[];
}

export function PortfolioForm({ fields }: PortfolioFormProps) {
  const {
    register,
    formState: { errors },
  } = useFormContext<PortfolioFormData>();

  return (
    <div className="space-y-8 bg-white p-8 rounded-lg shadow-md max-w-4xl mx-auto">
      <div>
        <label className="block text-xl font-medium text-secondary-01">
          Portfolio Title
        </label>
        <input
          {...register("title", { required: "Portfolio title is required" })}
          className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01"
          placeholder="Enter portfolio title"
        />
        {errors.title && (
          <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
        )}
      </div>

      {fields.map((field, index) => (
        <fieldset key={field.id} className="border p-6 rounded-md">
          <legend className="text-lg font-medium text-secondary-01 px-2">
            Description {index + 1}
          </legend>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-secondary-01">
                  Order
                </label>
                <input
                  type="number"
                  {...register(`descriptions.${index}.order` as const, {
                    required: "Order is required",
                    min: { value: 1, message: "Order must be at least 1" },
                  })}
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01"
                  placeholder="1"
                />
                {errors.descriptions?.[index]?.order && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.descriptions[index]?.order?.message}
                  </p>
                )}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-01">
                Description
              </label>
              <textarea
                {...register(`descriptions.${index}.description` as const, {
                  required: "Description is required.",
                })}
                rows={4}
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01"
                placeholder="Enter description text"
              />
              {errors.descriptions?.[index]?.description && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.descriptions[index]?.description?.message}
                </p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-secondary-01">
                Images (multiple can be selected)
              </label>
              <input
                type="file"
                {...register(`descriptions.${index}.images` as const)}
                multiple
                accept="image/*"
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-01 file:text-white hover:file:bg-primary-01/90"
              />
            </div>
          </div>
        </fieldset>
      ))}
    </div>
  );
}
