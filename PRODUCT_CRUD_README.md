# Product CRUD Implementation

This document describes the complete CRUD (Create, Read, Update, Delete) implementation for Products using the existing API hooks and services.

## Overview

The Product CRUD system provides a complete interface for managing products with the following features:

- **Create**: Add new products with form validation and file uploads
- **Read**: List all products with pagination and filtering
- **Update**: Edit existing products with pre-populated forms
- **Delete**: Remove products with confirmation dialogs

## Architecture

### API Layer (`src/services/api/use-product-api.ts`)

The API layer provides hooks for all CRUD operations:

```typescript
export const useProductsApi = () => {
  return {
    useGetProducts,    // Fetch all products
    useGetProduct,     // Fetch single product by ID
    useCreateProduct,  // Create new product
    useUpdateProduct,  // Update existing product
    useDeleteProduct,  // Delete product
  };
};
```

**Endpoints:**
- `GET /products` - List all products
- `GET /products/:id` - Get single product
- `POST /products` - Create new product (with FormData)
- `PATCH /products/:id` - Update product (with FormData)
- `DELETE /products/:id` - Delete product

### Form Management (`src/sections/product/form/`)

**Components:**
- `ProductForm` - Main form component with sections for basic info, media, applications, key benefits, and specifications
- `useProductForm` - Custom hook managing form state, validation, and API integration

**Features:**
- React Hook Form integration
- File upload support for images and videos
- Dynamic field arrays for specifications, applications, and key benefits
- Form validation with error handling
- Auto-navigation after successful operations

### View/List Management (`src/sections/product/view/`)

**Components:**
- `ProductView` - Table-based product listing with expand/collapse details
- `ProductDetails` - Detailed view component with tabbed interface
- `useProductView` - Custom hook managing list state, modals, and actions

**Features:**
- Expandable product rows
- Delete confirmation modals
- Cover/homepage toggle functionality
- Type name resolution
- Loading and error states

## Usage

### 1. List Products

```typescript
import { useProductView } from '../sections/product/view/use-product-view';
import { ProductView } from '../sections/product/view/product-view';

const ProductListPage = () => {
  const productViewProps = useProductView();
  return <ProductView {...productViewProps} />;
};
```

### 2. Create Product

Navigate to `/dashboard/products/add` or use the ProductFormPage component:

```typescript
import ProductFormPage from '../pages/product/product-form-page';

// The form automatically detects create mode when no ID is present
```

### 3. Edit Product

Navigate to `/dashboard/products/edit/:id` or use the ProductFormPage component:

```typescript
// The form automatically detects edit mode and loads existing data
```

### 4. Delete Product

Use the delete button in the ProductView component, which triggers a confirmation modal.

## Data Flow

### Create/Update Flow

1. User fills out the ProductForm
2. Form data is converted to FormData object
3. FormData is sent to API via useCreateProduct or useUpdateProduct
4. On success, user is redirected to products list
5. Product list is automatically refreshed via React Query

### Read Flow

1. useGetProducts hook fetches products list
2. Data is cached and managed by React Query
3. ProductView component renders the list
4. Individual product details can be expanded inline

### Delete Flow

1. User clicks delete button
2. Confirmation modal appears
3. On confirmation, useDeleteProduct is called
4. Product list is automatically refreshed

## File Structure

```
src/
├── services/api/
│   └── use-product-api.ts          # API hooks and endpoints
├── sections/product/
│   ├── form/
│   │   ├── product-form.tsx        # Form component
│   │   └── use-product-form.ts     # Form logic hook
│   ├── view/
│   │   ├── product-view.tsx        # List component
│   │   ├── product-details.tsx     # Detail component
│   │   └── use-product-view.ts     # List logic hook
│   └── types/
│       └── types.ts                # Form-specific types
├── pages/product/
│   ├── product-form-page.tsx       # Create/Edit page
│   ├── product-view-page.tsx       # List page
│   └── products-crud-page.tsx      # Combined CRUD page
└── tests/
    └── product-crud.test.tsx       # CRUD tests
```

## Routes

The following routes are configured in `src/routes/AppRouter.tsx`:

- `/dashboard/products` - Product list view
- `/dashboard/products/add` - Create new product
- `/dashboard/products/edit/:id` - Edit existing product

## Types

### Main Product Type (`src/sections/type/types/types.ts`)

```typescript
export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  mini_description: string;
  type_id: string;
  categories: string;
  tags: string;
  isCover?: boolean;
  images: (File | string)[];
  video_file?: File | string;
  specifications: SpecificationItem[];
  applications: ApplicationItem[];
  key_benefits: KeyBenefitItem[];
}
```

### Form Data Type (`src/sections/product/types/types.ts`)

```typescript
export interface ProductFormData {
  name: string;
  description: string;
  mini_description: string;
  type_id: string;
  categories: string;
  tags: string;
  images: FileList;
  video_file?: FileList;
  specifications: {
    items: SpecificationItem[];
    images: FileList;
  };
  applications: {
    items: { value: string }[];
    images: FileList;
  };
  key_benefits: {
    items: { value: string }[];
    images: FileList;
  };
}
```

## Testing

Run the CRUD tests:

```bash
npm test src/tests/product-crud.test.tsx
```

The tests cover:
- Product list rendering
- Create product navigation
- API integration mocking
- Component interaction

## Integration Notes

- Uses existing `useApiServices` hook for consistent API handling
- Integrates with React Query for caching and state management
- Uses existing UI components and styling patterns
- Follows the established routing and navigation patterns
- Compatible with existing authentication and layout systems
