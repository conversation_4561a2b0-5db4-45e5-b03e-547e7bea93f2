import { Form<PERSON>rovider } from "react-hook-form";
import { useTypeForm } from "./use-type-form";

const TypeForm = () => {
  const {
    formMethods,
    isEditMode,
    isLoading,
    isSubmitting,
    parentTypes,
    onSubmit,
    existingImage,
    createError,
    updateError,
  } = useTypeForm();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = formMethods;

  // Watch the image field to show preview
  const watchedImage = watch("image");

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md p-8">
        <h1 className="text-3xl font-bold text-secondary-01 mb-6">
          {isEditMode ? "Edit Type" : "Create New Type"}
        </h1>

        {/* Display API errors */}
        {(createError || updateError) && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">
              {createError?.message ||
                updateError?.message ||
                "An error occurred"}
            </p>
          </div>
        )}

        <FormProvider {...formMethods}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            noValidate
            className="space-y-6"
          >
            {/* Type Name */}
            <div>
              <label className="form-label">Type Name *</label>
              <input
                {...register("name", {
                  required: "Type name is required",
                  minLength: {
                    value: 2,
                    message: "Type name must be at least 2 characters",
                  },
                })}
                className="input-style"
                placeholder="Enter type name"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="form-label">Description *</label>
              <textarea
                {...register("description", {
                  required: "Description is required",
                  minLength: {
                    value: 10,
                    message: "Description must be at least 10 characters",
                  },
                })}
                className="input-style"
                rows={4}
                placeholder="Enter type description"
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Parent Type */}
            <div>
              <label className="form-label">Parent Type</label>
              <select {...register("parent_id")} className="input-style">
                <option value="">Select parent type (optional)</option>
                {parentTypes?.map((type: any) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
              {errors.parent_id && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.parent_id.message}
                </p>
              )}
            </div>

            {/* Image Upload */}
            <div>
              <label className="form-label">Type Image</label>

              {/* Show existing image in edit mode */}
              {isEditMode && existingImage && (
                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-2">Current image:</p>
                  <img
                    src={existingImage}
                    alt="Current type image"
                    className="w-32 h-32 object-cover rounded-md border"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Upload a new image to replace the current one
                  </p>
                </div>
              )}

              <input
                {...register("image")}
                type="file"
                accept="image/*"
                className="input-style"
              />
              {errors.image && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.image.message}
                </p>
              )}

              {/* Show preview of new selected image */}
              {watchedImage && watchedImage.length > 0 && (
                <div className="mt-4">
                  <p className="text-sm text-gray-600 mb-2">
                    New image preview:
                  </p>
                  <img
                    src={URL.createObjectURL(watchedImage[0])}
                    alt="New image preview"
                    className="w-32 h-32 object-cover rounded-md border"
                  />
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="pt-4 border-t">
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full py-3 px-4 font-semibold text-white cursor-pointer
                bg-primary-01/90 rounded-md hover:bg-primary-01
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-01
                disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    {isEditMode ? "Updating..." : "Creating..."}
                  </span>
                ) : isEditMode ? (
                  "Update Type"
                ) : (
                  "Create Type"
                )}
              </button>
            </div>
          </form>
        </FormProvider>
      </div>
    </div>
  );
};

export default TypeForm;
