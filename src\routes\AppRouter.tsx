import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { lazy, Suspense } from "react";
import DashboardLayout from "../components/layout/DashboardLayout";
import LoadingScreen from "../components/loading-screen/LoadingScreen";

const LoginPage = lazy(() => import("../pages/LoginPage"));
const DashboardPage = lazy(() => import("../pages/DashboardPage"));
const MessageViewPage = lazy(
  () => import("../pages/message/message-view-page")
);

const TypeViewPage = lazy(() => import("../pages/type/type-view-page"));
const TypeFormPage = lazy(() => import("../pages/type/type-form-page"));

const ProductViewPage = lazy(
  () => import("../pages/product/product-view-page")
);
const ProductFormPage = lazy(
  () => import("../pages/product/product-form-page")
);
const ProductDetailsPage = lazy(
  () => import("../pages/product/product-details-page")
);

const PortfolioViewPage = lazy(
  () => import("../pages/portfolio/potfolio-view-page")
);
const PortfolioFormPage = lazy(
  () => import("../pages/portfolio/potfolio-form-page")
);

const AppRouter = () => {
  const isAuthenticated = !!localStorage.getItem("authToken");

  return (
    <Router>
      <Suspense fallback={<LoadingScreen />}>
        <Routes>
          <Route path="/" element={<LoginPage />} />

          <Route path="/dashboard" element={<DashboardLayout />}>
            <Route index element={<DashboardPage />} />
            <Route path="messages" element={<MessageViewPage />} />

            {/* Type Routes  */}
            <Route path="types" element={<TypeViewPage />} />
            <Route path="types/add" element={<TypeFormPage />} />
            <Route path="types/edit/:id" element={<TypeFormPage />} />

            {/*  Product Routes  */}
            <Route path="products" element={<ProductViewPage />} />
            <Route path="products/add" element={<ProductFormPage />} />
            <Route path="products/edit/:id" element={<ProductFormPage />} />
            <Route
              path="products/details/:id"
              element={<ProductDetailsPage />}
            />

            {/* portfolios Routes */}
            <Route path="portfolios" element={<PortfolioViewPage />} />
            <Route path="portfolios/add" element={<PortfolioFormPage />} />
            <Route path="portfolios/edit/:id" element={<PortfolioFormPage />} />
          </Route>

          <Route
            path="*"
            element={<Navigate to={isAuthenticated ? "/dashboard" : "/"} />}
          />
        </Routes>
      </Suspense>
    </Router>
  );
};

export default AppRouter;
