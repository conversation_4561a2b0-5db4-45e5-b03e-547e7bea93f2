import { AxiosError } from "axios";
import { enqueueSnackbar } from "notistack";

export const useApiResult = () => {
  const handleApiSuccessWithSnackbar = () => {
    enqueueSnackbar({
      variant: "success",
      message: "success",
      anchorOrigin: {
        vertical: "bottom",
        horizontal: "right",
      },
    });
  };

  const handleApiErrorWithSnackbar = (error: AxiosError) => {
    enqueueSnackbar({
      variant: "error",
      message: error?.message,
      anchorOrigin: {
        vertical: "top",
        horizontal: "right",
      },
    });
  };

  return {
    handleApiSuccessWithSnackbar,
    handleApiErrorWithSnackbar,
  };
};

export default useApiResult;
