
interface Item {
  id: number;
  primary: string;
  secondary: string;
}

interface RecentActivityListProps {
  title: string;
  items: Item[];
}

const RecentActivityList: React.FC<RecentActivityListProps> = ({ title, items }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md h-full">
      <h3 className="text-lg font-bold text-primary-01 mb-4 pb-3 border-b border-gray-200">{title}</h3>
      <ul className="space-y-4">
        {items.map((item) => (
          <li key={item.id} className="flex flex-col pb-2 border-b border-gray-100 last:border-b-0">
            <span className="font-medium text-gray-700">{item.primary}</span>
            <span className="text-sm text-gray-500">{item.secondary}</span>
          </li>
        ))}
         {items.length === 0 && <p className="text-sm text-gray-400">No Data.</p>}
      </ul>
    </div>
  );
};

export default RecentActivityList;