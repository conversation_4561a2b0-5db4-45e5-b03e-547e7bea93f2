import TypeForm from '../../sections/type/form/type-form';
import { useTypeForm } from '../../sections/type/form/use-type-form';

const TypeFormPage = () => {
  const {
    handleSubmit,
    initialData,
    isSubmitting,
    isLoadingInitialData,
    pageTitle,
  } = useTypeForm();

  if (isLoadingInitialData) {
    return <div>Loading sub-type details...</div>;
  }

  return (
    <div>
      <h1 className="text-3xl font-bold text-secondary-01 mb-6">
        {pageTitle}
      </h1>
      <TypeForm
        onSubmit={handleSubmit}
        initialData={initialData}
        isSubmitting={isSubmitting}
      />
    </div>
  );
};

export default TypeFormPage;
