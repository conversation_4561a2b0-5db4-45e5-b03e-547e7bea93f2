import { Link } from "react-router-dom";
import type { Portfolio } from "../types/types";
import ConfirmationModal from "../../../components/common/ConfirmationModal";


interface PortfolioViewProps {
    portfolios: Portfolio[] | undefined;
    isLoading: boolean;
    error: Error | null;
    isModalOpen: boolean;
    isDeleting: boolean;
    openModal: (id: string) => void;
    closeModal: () => void;
    handleDelete: () => void;
}

export function PortfolioView({ portfolios, isLoading, error, openModal, closeModal, handleDelete, isModalOpen }: PortfolioViewProps) {

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>An error occurred: {error.message}</div>;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-secondary-01">Portfolio</h1>
        <Link
          to="/dashboard/portfolios/add"
          className="px-4 py-2 font-semibold text-white bg-primary-01/90 rounded-md hover:bg-primary-01"
        >
          + Add Portfolio
        </Link>
      </div>

      <div className="space-y-8">
        {portfolios?.map((portfolio) => (
          <div key={portfolio.id} className="bg-white p-6 rounded-lg shadow-md">
            <div className="flex justify-between items-start">
              <h2 className="text-2xl font-semibold text-primary-01 mb-4">
                {portfolio.name}
              </h2>
              <div className="flex space-x-4">
                <Link
                  to={`/dashboard/portfolios/edit/${portfolio.id}`}
                  className="text-blue-500 hover:underline font-semibold"
                >
                  Edit
                </Link>
                <button
                  onClick={() => openModal(portfolio.id)}
                  className="text-red-500 hover:underline font-semibold cursor-pointer "
                >
                  Delete
                </button>
              </div>
            </div>
            <div className="grid md:grid-cols-3 gap-6">
              {portfolio.items.map((item, index) => (
                <div key={index} className="border p-4 rounded-md">
                  <p className="text-secondary-01 mb-3">{item.text}</p>
                  <div className="grid grid-cols-2 gap-2">
                    {Array.isArray(item.images) && item.images.map((img, imgIndex) => (
                      <img
                        key={imgIndex}
                        src={img as string} 
                        alt={`Portfolio item ${index + 1}`}
                        className="w-full h-auto rounded-md object-cover"
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      <ConfirmationModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onConfirm={handleDelete}
        title="Delete Portfolio"
        message="Are you sure you want to delete this portfolio item?"
      />
    </div>
  );
}