import { useQuery } from "@tanstack/react-query";
import { useProductsApi } from "../services/api/use-product-api";
import useMessagesApi from "../services/api/use-messages-api";
import { usePortfolioApi } from "../services/api/use-portfolio-api";
import { useTypeApi } from "../services/api/use-type-api";

interface DashboardStats {
  newMessages: number;
  totalProducts: number;
  totalCategories: number;
  portfolioItems: number;
  recentMessages: { id: number; primary: string; secondary: string }[];
  recentProducts: { id: number; primary: string; secondary: string }[];
  totalPages: {
    products: number;
    messages: number;
    portfolios: number;
    types: number;
  };
  isLoading: boolean;
  hasError: boolean;
}

export const useDashboardData = () => {
  // Initialize API hooks
  const { useGetProducts } = useProductsApi();
  const { useGetMessages } = useMessagesApi();
  const { useGetPortfolios } = usePortfolioApi();
  const { useGetSubTypes } = useTypeApi();

  // Fetch data from all endpoints
  const {
    data: productsData,
    isLoading: isLoadingProducts,
    isError: isErrorProducts,
  } = useGetProducts(1, 5);
  const {
    data: messagesData,
    isLoading: isLoadingMessages,
    isError: isErrorMessages,
  } = useGetMessages(1, 5);
  const {
    data: portfoliosData,
    isLoading: isLoadingPortfolios,
    isError: isErrorPortfolios,
  } = useGetPortfolios(1, 5);
  const {
    data: typesData,
    isLoading: isLoadingTypes,
    isError: isErrorTypes,
  } = useGetSubTypes(1, 10);

  // Combine all loading states
  const isLoading =
    isLoadingProducts ||
    isLoadingMessages ||
    isLoadingPortfolios ||
    isLoadingTypes;
  const isError =
    isErrorProducts || isErrorMessages || isErrorPortfolios || isErrorTypes;

  // Process and combine data
  const processedData: DashboardStats = {
    // Statistics
    totalProducts: productsData?.pagination?.total || 0,
    newMessages: messagesData?.pagination?.total || 0,
    portfolioItems: portfoliosData?.pagination?.total || 0,
    totalCategories: typesData?.pagination?.total || 0,

    // Recent items
    recentMessages:
      messagesData?.data?.slice(0, 5).map((message) => ({
        id: message.id,
        primary: message.name,
        secondary: message.email || message.phone || "Contact Form",
      })) || [],

    recentProducts:
      productsData?.data?.slice(0, 5).map((product: any) => ({
        id: product.id,
        primary: product.name,
        secondary: product.sub_type?.name || product.type?.name || "Product",
      })) || [],

    // Pagination info for detailed statistics
    totalPages: {
      products: productsData?.pagination?.total_pages || 0,
      messages: messagesData?.pagination?.total_pages || 0,
      portfolios: portfoliosData?.pagination?.total_pages || 0,
      types: typesData?.pagination?.total_pages || 0,
    },

    isLoading,
    hasError: isError,
  };

  return {
    data: processedData,
    isLoading,
    isError,
    error: isError ? new Error("Failed to load dashboard data") : null,
    // Individual data for more detailed access if needed
    rawData: {
      products: productsData,
      messages: messagesData,
      portfolios: portfoliosData,
      types: typesData,
    },
  };
};
