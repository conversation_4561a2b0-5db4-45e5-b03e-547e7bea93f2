
import { useQuery } from '@tanstack/react-query';

interface DashboardStats {
  newMessages: number;
  totalProducts: number;
  totalCategories: number;
  portfolioItems: number;
  recentMessages: { id: number; primary: string; secondary: string; }[];
  recentProducts: { id: number; primary: string; secondary: string; }[];
}


const fetchDashboardData = async (): Promise<DashboardStats> => {

  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        newMessages: 15,
        totalProducts: 120,
        totalCategories: 12,
        portfolioItems: 35,
        recentMessages: [
          { id: 1, primary: '<PERSON>', secondary: 'Website Form' },
          { id: 2, primary: '<PERSON>', secondary: 'Social Media' },
        ],
        recentProducts: [
          { id: 1, primary: 'Accumulated Timing Belt Conveyor', secondary: 'Added to Conveyor Systems category' },
          { id: 2, primary: 'Accumulated Timing Belt Conveyor', secondary: 'Added to Conveyor Systems category' },
        ],
      });
    }, 1000);
  });
};

export const useDashboardData = () => {
  return useQuery<DashboardStats, Error>({
    queryKey: ['dashboardData'],
    queryFn: fetchDashboardData,
  });
};