import { useState, useEffect } from "react";
import type { Product, SpecificationItem, ApplicationItem, KeyBenefitItem, MediaItem } from "../types/types";
import { TabButton } from "../../../components/common/TabButton";

// Helper component to render the content of a section
const SectionDisplay = ({
  items,
  mediaItems,
}: {
  items: (string | SpecificationItem | ApplicationItem | KeyBenefitItem)[];
  mediaItems?: MediaItem[];
}) => (
  <div className="flex flex-col md:flex-row gap-8">
    <div className="flex-1">
      <ul className="list-disc list-inside space-y-3">
        {items.map((item, index) => (
          <li key={index} className="text-gray-800">
            {typeof item === "object" && "title" in item ? (
              <span>
                <span className="font-semibold">{item.title}:</span>{" "}
                {"content" in item ? item.content : ""}
              </span>
            ) : (
              String(item)
            )}
          </li>
        ))}
      </ul>
    </div>
    {mediaItems && mediaItems.length > 0 && (
      <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 gap-4">
        {mediaItems.map((media, idx) => (
          <img
            key={idx}
            src={media.url}
            alt={`detail-${idx}`}
            className="w-full h-auto object-cover rounded-lg shadow-md"
          />
        ))}
      </div>
    )}
  </div>
);

export function ProductDetails({ product }: { product: Product }) {
  const [activeTab, setActiveTab] = useState("Description");

  const availableSections = {
    Description: product.description,
    Applications: product.applications,
    "Key Benefits": product.key_benefits,
    Specifications: product.specifications,
  };

  type SectionKey = keyof typeof availableSections;

  useEffect(() => {
    const firstAvailableTab = (
      Object.keys(availableSections) as SectionKey[]
    ).find((key) => {
      const section = availableSections[key];
      if (!section) return false;
      // Check if the section is an array with length > 0 or a non-empty string
      if (Array.isArray(section)) return section.length > 0;
      return typeof section === "string" && section.trim() !== "";
    });
    if (firstAvailableTab) {
      setActiveTab(firstAvailableTab);
    }
  }, [product]);

  return (
    <div className="space-y-4 p-4 bg-white rounded-lg border">
      <div className="border-b border-gray-200">
        <nav className="flex flex-wrap -mb-px">
          {(Object.keys(availableSections) as SectionKey[]).map((key) => {
            const section = availableSections[key];
            const hasContent =
              section &&
              (typeof section === "string" ||
                (Array.isArray(section) && section.length > 0));
            return hasContent ? (
              <TabButton
                key={key}
                label={key}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
              />
            ) : null;
          })}
        </nav>
      </div>

      <div className="p-4 min-h-[150px]">
        {activeTab === "Description" && (
          <p className="text-gray-700 whitespace-pre-line">
            {product.description}
          </p>
        )}
        {activeTab === "Applications" && product.applications && product.applications.length > 0 && (
          <SectionDisplay
            items={product.applications}
            mediaItems={product.applications.flatMap(app => app.media ? [app.media] : []) as any}
          />
        )}
        {activeTab === "Key Benefits" && product.key_benefits && product.key_benefits.length > 0 && (
          <SectionDisplay
            items={product.key_benefits}
            mediaItems={product.key_benefits.flatMap(benefit => benefit.media ? [benefit.media] : [])}
          />
        )}
        {activeTab === "Specifications" && product.specifications && product.specifications.length > 0 && (
          <SectionDisplay
            items={product.specifications}
            mediaItems={[]}
          />
        )}
      </div>
    </div>
  );
}
