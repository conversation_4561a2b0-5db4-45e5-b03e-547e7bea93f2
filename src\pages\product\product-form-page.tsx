import { FormProvider } from "react-hook-form";
import { useProductForm } from "../../sections/product/form/use-product-form";
import { ProductForm } from "../../sections/product/form/product-form";

const ProductFormPage = () => {
  const {
    formMethods,
    onSubmit,
    subTypes,
    existingImages,
    existingApplicationImages,
    existingKeyBenefitImages,
    isEditMode,
    productToEdit,
    ...rest
  } = useProductForm();

  // Show loading state while data is being fetched
  if (rest.isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-01 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading product data...</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-3xl font-bold text-secondary-01 mb-6">
        {isEditMode ? "Edit Product" : "Add New Product"}
      </h1>

      {/* Show form errors if any */}
      {Object.keys(formMethods.formState.errors).length > 0 &&
        formMethods.formState.isSubmitted && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="text-red-800 font-semibold mb-2">
              Please fix the following errors:
            </h3>
            <ul className="text-red-700 text-sm space-y-1">
              {Object.entries(formMethods.formState.errors).map(
                ([field, error]) => (
                  <li key={field}>• {error?.message}</li>
                )
              )}
            </ul>
          </div>
        )}

      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)} noValidate>
          <ProductForm
            {...rest}
            productToEdit={productToEdit}
            types={subTypes}
            isEditMode={isEditMode}
            existingImages={existingImages}
            existingApplicationImages={existingApplicationImages}
            existingKeyBenefitImages={existingKeyBenefitImages}
          />
          <div className="mt-8 pt-4 border-t">
            <button
              type="submit"
              disabled={rest.isSubmitting || rest.isLoading}
              className="w-full py-3 btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {rest.isSubmitting ? (
                <span className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditMode ? "Updating..." : "Creating..."}
                </span>
              ) : isEditMode ? (
                "Save Changes"
              ) : (
                "Create Product"
              )}
            </button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default ProductFormPage;
