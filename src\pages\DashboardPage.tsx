import { useState } from "react";
import {
  MailIcon,
  PackageIcon,
  TagIcon,
  GalleryVerticalIcon,
  PlusCircleIcon,
  EyeIcon,
  EditIcon,
  TrashIcon,
  SettingsIcon,
  DatabaseIcon,
  ApiIcon,
  BarChartIcon,
} from "../components/common/Icons";
import { useDashboardData } from "../hooks/useDashboardData";
import StatCard from "../components/common/StatCard";
import RecentActivityList from "../components/common/RecentActivityList";
import { Link } from "react-router-dom";

const DashboardPage = () => {
  const { data, isLoading, isError, error } = useDashboardData();
  const [activeTab, setActiveTab] = useState("overview");

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-01 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center h-screen text-red-500">
        <div className="text-center">
          <div className="text-6xl mb-4">⚠️</div>
          <p className="text-xl font-semibold">Error loading dashboard</p>
          <p className="text-gray-600 mt-2">{error && error.message}</p>
        </div>
      </div>
    );
  }

  const endpoints = [
    {
      name: "Products",
      endpoint: "/products",
      description: "Manage industrial products and equipment",
      methods: ["GET", "POST", "PUT", "DELETE"],
      features: [
        "Pagination",
        "Search",
        "Categories",
        "Images",
        "Specifications",
      ],
      icon: <PackageIcon className="w-6 h-6" />,
      color: "bg-green-500",
      count: data?.totalProducts || 0,
      actions: [
        {
          label: "View All",
          link: "/dashboard/products",
          icon: <EyeIcon className="w-4 h-4" />,
        },
        {
          label: "Add New",
          link: "/dashboard/products/add",
          icon: <PlusCircleIcon className="w-4 h-4" />,
        },
      ],
    },
    {
      name: "Messages",
      endpoint: "/user-messages",
      description: "Customer contact messages and inquiries",
      methods: ["GET", "DELETE"],
      features: ["Contact Forms", "Email Integration", "Response Tracking"],
      icon: <MailIcon className="w-6 h-6" />,
      color: "bg-blue-500",
      count: data?.newMessages || 0,
      actions: [
        {
          label: "View All",
          link: "/dashboard/messages",
          icon: <EyeIcon className="w-4 h-4" />,
        },
      ],
    },
    {
      name: "Product Types",
      endpoint: "/types",
      description: "Product categories and type management",
      methods: ["GET", "POST", "PUT", "DELETE"],
      features: [
        "Hierarchical Structure",
        "Media Support",
        "Applications",
        "Key Benefits",
      ],
      icon: <TagIcon className="w-6 h-6" />,
      color: "bg-purple-500",
      count: data?.totalCategories || 0,
      actions: [
        {
          label: "View All",
          link: "/dashboard/types",
          icon: <EyeIcon className="w-4 h-4" />,
        },
        {
          label: "Add New",
          link: "/dashboard/types/add",
          icon: <PlusCircleIcon className="w-4 h-4" />,
        },
      ],
    },
    {
      name: "Portfolio",
      endpoint: "/portfolios",
      description: "Showcase projects and work samples",
      methods: ["GET", "POST", "PUT", "DELETE"],
      features: [
        "Image Galleries",
        "Descriptions",
        "Media Management",
        "Ordering",
      ],
      icon: <GalleryVerticalIcon className="w-6 h-6" />,
      color: "bg-orange-500",
      count: data?.portfolioItems || 0,
      actions: [
        {
          label: "View All",
          link: "/dashboard/portfolios",
          icon: <EyeIcon className="w-4 h-4" />,
        },
        {
          label: "Add New",
          link: "/dashboard/portfolios/add",
          icon: <PlusCircleIcon className="w-4 h-4" />,
        },
      ],
    },
  ];

  const systemInfo = [
    {
      label: "Total Endpoints",
      value: "4",
      icon: <ApiIcon className="w-5 h-5 text-blue-500" />,
      description: "Products, Messages, Types, Portfolio",
    },
    {
      label: "Total Records",
      value: (
        (data?.totalProducts || 0) +
        (data?.newMessages || 0) +
        (data?.portfolioItems || 0) +
        (data?.totalCategories || 0)
      ).toString(),
      icon: <DatabaseIcon className="w-5 h-5 text-green-500" />,
      description: "Across all database tables",
    },
    {
      label: "Active Pages",
      value: (
        (data?.totalPages?.products || 0) +
        (data?.totalPages?.messages || 0) +
        (data?.totalPages?.portfolios || 0) +
        (data?.totalPages?.types || 0)
      ).toString(),
      icon: <GalleryVerticalIcon className="w-5 h-5 text-purple-500" />,
      description: "Total pagination pages",
    },
    {
      label: "API Status",
      value: data?.isLoading ? "Loading" : data?.hasError ? "Error" : "Online",
      icon: <SettingsIcon className="w-5 h-5 text-orange-500" />,
      description: "System health status",
    },
  ];

  return (
    <div className="min-h-screen space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-01 to-secondary-01 rounded-lg p-6 text-white">
        <h1 className="text-3xl font-bold mb-2">Admin Dashboard</h1>
        <p className="text-primary-01/80">
          Manage your industrial equipment platform
        </p>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              {
                id: "overview",
                label: "Overview",
                icon: <EyeIcon className="w-4 h-4" />,
              },
              {
                id: "endpoints",
                label: "API Endpoints",
                icon: <ApiIcon className="w-4 h-4" />,
              },
              {
                id: "system",
                label: "System Info",
                icon: <SettingsIcon className="w-4 h-4" />,
              },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? "border-primary-01 text-primary-01"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === "overview" && (
        <div className="space-y-8">
          {/* Main Stats Cards */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Messages"
              value={data?.newMessages.toString() ?? "0"}
              icon={<MailIcon className="text-blue-500" />}
              colorClass="bg-blue-100"
            />
            <StatCard
              title="Products Available"
              value={data?.totalProducts.toString() ?? "0"}
              icon={<PackageIcon className="text-green-500" />}
              colorClass="bg-green-100"
            />
            <StatCard
              title="Product Types"
              value={data?.totalCategories.toString() ?? "0"}
              icon={<TagIcon className="text-purple-500" />}
              colorClass="bg-purple-100"
            />
            <StatCard
              title="Portfolio Items"
              value={data?.portfolioItems.toString() ?? "0"}
              icon={<GalleryVerticalIcon className="text-orange-500" />}
              colorClass="bg-orange-100"
            />
          </div>

          {/* Enhanced Statistics Section */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <BarChartIcon className="w-6 h-6 text-primary-01" />
              Detailed Statistics
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  {data?.totalPages?.messages || 0}
                </div>
                <div className="text-sm text-blue-700 font-medium">
                  Message Pages
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  {data?.newMessages ? Math.ceil(data.newMessages / 10) : 0}{" "}
                  pages of 10
                </div>
              </div>

              <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  {data?.totalPages?.products || 0}
                </div>
                <div className="text-sm text-green-700 font-medium">
                  Product Pages
                </div>
                <div className="text-xs text-green-600 mt-1">
                  {data?.totalProducts ? Math.ceil(data.totalProducts / 10) : 0}{" "}
                  pages of 10
                </div>
              </div>

              <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 mb-2">
                  {data?.totalPages?.types || 0}
                </div>
                <div className="text-sm text-purple-700 font-medium">
                  Type Pages
                </div>
                <div className="text-xs text-purple-600 mt-1">
                  {data?.totalCategories
                    ? Math.ceil(data.totalCategories / 10)
                    : 0}{" "}
                  pages of 10
                </div>
              </div>

              <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg">
                <div className="text-2xl font-bold text-orange-600 mb-2">
                  {data?.totalPages?.portfolios || 0}
                </div>
                <div className="text-sm text-orange-700 font-medium">
                  Portfolio Pages
                </div>
                <div className="text-xs text-orange-600 mt-1">
                  {data?.portfolioItems
                    ? Math.ceil(data.portfolioItems / 10)
                    : 0}{" "}
                  pages of 10
                </div>
              </div>
            </div>
          </div>

          {/* System Health Overview */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <DatabaseIcon className="w-6 h-6 text-primary-01" />
              System Overview
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg">
                <ApiIcon className="w-8 h-8 text-indigo-600 mx-auto mb-3" />
                <div className="text-lg font-bold text-indigo-600 mb-1">4</div>
                <div className="text-sm text-indigo-700 font-medium">
                  Active Endpoints
                </div>
                <div className="text-xs text-indigo-600 mt-1">
                  All systems operational
                </div>
              </div>

              <div className="text-center p-4 bg-gradient-to-br from-emerald-50 to-emerald-100 rounded-lg">
                <DatabaseIcon className="w-8 h-8 text-emerald-600 mx-auto mb-3" />
                <div className="text-lg font-bold text-emerald-600 mb-1">
                  {(data?.totalProducts || 0) +
                    (data?.newMessages || 0) +
                    (data?.portfolioItems || 0) +
                    (data?.totalCategories || 0)}
                </div>
                <div className="text-sm text-emerald-700 font-medium">
                  Total Records
                </div>
                <div className="text-xs text-emerald-600 mt-1">
                  Across all tables
                </div>
              </div>

              <div className="text-center p-4 bg-gradient-to-br from-rose-50 to-rose-100 rounded-lg">
                <SettingsIcon className="w-8 h-8 text-rose-600 mx-auto mb-3" />
                <div className="text-lg font-bold text-rose-600 mb-1">REST</div>
                <div className="text-sm text-rose-700 font-medium">
                  API Architecture
                </div>
                <div className="text-xs text-rose-600 mt-1">
                  Modern & scalable
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              <RecentActivityList
                title="Latest Messages Received"
                items={data?.recentMessages ?? []}
              />
            </div>
            <div className="space-y-8">
              {/* Quick Actions */}
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-bold text-primary-01 mb-4 pb-3 border-b border-gray-200 flex items-center gap-2">
                  <PlusCircleIcon className="w-5 h-5" />
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <Link
                    to="/dashboard/products/add"
                    className="w-full flex items-center justify-center cursor-pointer text-white px-4 py-3 rounded-lg bg-secondary-01/90 hover:bg-secondary-01 transition font-medium"
                  >
                    <PlusCircleIcon className="mr-2 h-5 w-5" />
                    Add New Product
                  </Link>
                  <Link
                    to="/dashboard/portfolios/add"
                    className="w-full flex items-center justify-center cursor-pointer text-white px-4 py-3 rounded-lg bg-primary-01/90 hover:bg-primary-01 transition font-medium"
                  >
                    <PlusCircleIcon className="mr-2 h-5 w-5" />
                    Add Portfolio Work
                  </Link>
                  <Link
                    to="/dashboard/types/add"
                    className="w-full flex items-center justify-center cursor-pointer text-white px-4 py-3 rounded-lg bg-purple-500/90 hover:bg-purple-500 transition font-medium"
                  >
                    <PlusCircleIcon className="mr-2 h-5 w-5" />
                    Add Product Type
                  </Link>
                  <Link
                    to="/dashboard/messages"
                    className="w-full flex items-center justify-center cursor-pointer text-gray-700 px-4 py-3 rounded-lg bg-gray-100 hover:bg-gray-200 transition font-medium"
                  >
                    <MailIcon className="mr-2 h-5 w-5" />
                    View Messages
                  </Link>
                </div>
              </div>
              <RecentActivityList
                title="Latest Products Added"
                items={data?.recentProducts ?? []}
              />
            </div>
          </div>
        </div>
      )}

      {activeTab === "endpoints" && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <ApiIcon className="w-6 h-6 text-primary-01" />
              API Endpoints Overview
            </h2>
            <p className="text-gray-600 mb-6">
              Comprehensive overview of all available API endpoints and their
              capabilities
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {endpoints.map((endpoint, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-md overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div
                        className={`p-2 rounded-lg ${endpoint.color} text-white`}
                      >
                        {endpoint.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">
                          {endpoint.name}
                        </h3>
                        <code className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {endpoint.endpoint}
                        </code>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        {endpoint.count}
                      </div>
                      <div className="text-sm text-gray-500">Total Items</div>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4">{endpoint.description}</p>

                  {/* HTTP Methods */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">
                      HTTP Methods
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {endpoint.methods.map((method) => (
                        <span
                          key={method}
                          className={`px-2 py-1 text-xs font-medium rounded ${
                            method === "GET"
                              ? "bg-green-100 text-green-800"
                              : method === "POST"
                              ? "bg-blue-100 text-blue-800"
                              : method === "PUT"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {method}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-700 mb-2">
                      Features
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {endpoint.features.map((feature) => (
                        <span
                          key={feature}
                          className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    {endpoint.actions.map((action, actionIndex) => (
                      <Link
                        key={actionIndex}
                        to={action.link}
                        className="flex items-center gap-1 px-3 py-2 text-sm font-medium text-primary-01 bg-primary-01/10 rounded-lg hover:bg-primary-01/20 transition-colors"
                      >
                        {action.icon}
                        {action.label}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === "system" && (
        <div className="space-y-6">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 flex items-center gap-2">
              <SettingsIcon className="w-6 h-6 text-primary-01" />
              System Information
            </h2>
            <p className="text-gray-600 mb-6">
              Technical details and system capabilities
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              {systemInfo.map((info, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center gap-2 mb-2">
                    {info.icon}
                    <span className="text-sm font-medium text-gray-700">
                      {info.label}
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-gray-900 mb-1">
                    {info.value}
                  </div>
                  <div className="text-xs text-gray-600">
                    {info.description}
                  </div>
                </div>
              ))}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* API Features */}
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  API Features
                </h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    RESTful API Architecture
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Pagination Support
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    File Upload Handling
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Error Handling
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    TypeScript Support
                  </li>
                </ul>
              </div>

              {/* Data Models */}
              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Data Models
                </h3>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Products with Categories
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Media Management
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Portfolio Descriptions
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Contact Messages
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Product Types & Specifications
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
export default DashboardPage;
