
import { MailIcon, PackageIcon, TagIcon, GalleryVerticalIcon, PlusCircleIcon } from '../components/common/Icons';
import { useDashboardData } from '../hooks/useDashboardData';
import StatCard from '../components/common/StatCard';
import RecentActivityList from '../components/common/RecentActivityList';
import { Link } from 'react-router-dom';

const DashboardPage = () => {
  const { data, isLoading, isError, error } = useDashboardData();

  if (isLoading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }

  if (isError) {
    return <div className="flex items-center justify-center h-screen text-red-500">Error: {error.message}</div>;
  }

  return (
    <div className="min-h-screen">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard title="New message" value={data?.newMessages.toString() ?? '0'} icon={<MailIcon className="text-blue-500" />} colorClass="bg-blue-100" />
          <StatCard title="Product available" value={data?.totalProducts.toString() ?? '0'} icon={<PackageIcon className="text-green-500" />} colorClass="bg-green-100" />
          <StatCard title="Category" value={data?.totalCategories.toString() ?? '0'} icon={<TagIcon className="text-purple-500" />} colorClass="bg-purple-100" />
          <StatCard title="Work in portfolio " value={data?.portfolioItems.toString() ?? '0'} icon={<GalleryVerticalIcon className="text-orange-500" />} colorClass="bg-orange-100" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">    
            <RecentActivityList title="Latest messages received" items={data?.recentMessages ?? []} />
          </div>
          <div className="space-y-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-bold text-primary-01 mb-4 pb-3 border-b border-gray-200"> Quick actions</h3>
              <div className="space-y-3">
                <Link to={'/dashboard/products/add'} className="w-full flex items-center justify-center  cursor-pointer
                text-white px-4 py-3 rounded-lg bg-secondary-01/90 hover:bg-secondary-01  transition font-medium">
                  <PlusCircleIcon className="mr-2 h-5 w-5" />
                  Add New Product
                </Link>
                <Link to={'/dashboard/portfolios/add'} className="w-full flex items-center justify-center  cursor-pointer
                text-white px-4 py-3 rounded-lg bg-primary-01/90 hover:bg-primary-01  transition font-medium">
                  <PlusCircleIcon className="mr-2 h-5 w-5" />
                 Add work to portfolio
                </Link>
              </div>
            </div>
            <RecentActivityList title="Latest Products Added" items={data?.recentProducts ?? []} />
          </div>
        </div>
    </div>
  );
};
export default DashboardPage