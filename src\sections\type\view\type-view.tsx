import { Link } from "react-router-dom";
import { useTypeView } from "./use-type-view";
import ConfirmationModal from "../../../components/common/ConfirmationModal";

const TypeView = () => {
  const {
    types,
    isLoading,
    error,
    expandedTypeId,
    handleToggleExpand,
    isDeleteModalOpen,
    isDeleting,
    selectedType,
    openDeleteModal,
    closeDeleteModal,
    handleConfirmDelete,
    // Pagination props
    currentPage,
    totalPages,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    totalItems,
  } = useTypeView();

  if (isLoading)
    return (
      <div className="flex justify-center items-center h-64">
        Loading types...
      </div>
    );
  if (error)
    return <div className="text-red-500 text-center">Error loading types</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-secondary-01">
          Types Management
        </h1>
        <Link
          to="/dashboard/types/add"
          className="bg-primary-01 text-white px-4 py-2 rounded-md hover:bg-primary-01/90 transition-colors"
        >
          Add New Type
        </Link>
      </div>

      {/* Types Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Parent Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Image
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {types?.map((type: any) =>
                [
                  <tr key={type.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {type.name}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {type.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs truncate">
                        {type.description || "No description"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {type.parent_id
                          ? `Parent ID: ${type.parent_id}`
                          : "Root Type"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {type.image || type.media?.[0]?.url ? (
                        <img
                          src={type.image || type.media?.[0]?.url}
                          alt={type.name}
                          className="h-12 w-12 object-cover rounded-md"
                        />
                      ) : (
                        <div className="h-12 w-12 bg-gray-200 rounded-md flex items-center justify-center">
                          <span className="text-gray-400 text-xs">
                            No Image
                          </span>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Link
                          to={`/dashboard/types/edit/${type.id}`}
                          className="text-indigo-600 hover:text-indigo-900 transition-colors"
                        >
                          Edit
                        </Link>
                        <button
                          onClick={() => openDeleteModal(type)}
                          className="text-red-600 hover:text-red-900 transition-colors"
                        >
                          Delete
                        </button>
                        <button
                          onClick={() => handleToggleExpand(type.id.toString())}
                          className="text-gray-600 hover:text-gray-900 transition-colors"
                        >
                          {expandedTypeId === type.id.toString()
                            ? "Collapse"
                            : "Expand"}
                        </button>
                      </div>
                    </td>
                  </tr>,
                  // Expanded row for additional details
                  expandedTypeId === type.id.toString() && (
                    <tr key={`${type.id}-expanded`}>
                      <td colSpan={5} className="px-6 py-4 bg-gray-50">
                        <div className="space-y-4">
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">
                              Full Description:
                            </h4>
                            <p className="text-sm text-gray-700">
                              {type.description || "No description available"}
                            </p>
                          </div>
                          {type.products && type.products.length > 0 && (
                            <div>
                              <h4 className="font-medium text-gray-900 mb-2">
                                Products ({type.products.length}):
                              </h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                {type.products
                                  .slice(0, 6)
                                  .map((product: any) => (
                                    <div
                                      key={product.id}
                                      className="bg-white p-2 rounded border text-sm"
                                    >
                                      {product.name}
                                    </div>
                                  ))}
                                {type.products.length > 6 && (
                                  <div className="bg-white p-2 rounded border text-sm text-gray-500">
                                    +{type.products.length - 6} more...
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  ),
                ].filter(Boolean)
              )}
            </tbody>
          </table>
        </div>

        {/* Simple Pagination */}
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage <= 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{" "}
                <span className="font-medium">
                  {(currentPage - 1) * itemsPerPage + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(currentPage * itemsPerPage, totalItems)}
                </span>{" "}
                of <span className="font-medium">{totalItems}</span> results
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <select
                value={itemsPerPage}
                onChange={(e) =>
                  handleItemsPerPageChange(Number(e.target.value))
                }
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
              </select>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleConfirmDelete}
        isConfirming={isDeleting}
        title="Delete Type"
        message={`Are you sure you want to delete "${selectedType?.name}"? This action cannot be undone.`}
      />
    </div>
  );
};

export default TypeView;
