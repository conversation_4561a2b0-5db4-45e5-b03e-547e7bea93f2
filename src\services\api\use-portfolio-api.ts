import useApiServices from "../hooks/use-api-services";

import axiosInstance from "../../utils/axios";
import type { Product } from "../../sections/type/types/types";
// Define the API endpoints for Portfolio
export const ProductEndpoints = {
  list: "/portfolios",
  details: "/portfolios",
};

export type PortfolioItem = {
  message: string;
  data: Portfolio;
};
// Define the API response types
export type PortfolioResponse = {
  status: string; // e.g., "success"
  message: string; // e.g., "Operation Success"
  data: Portfolio[];
  pagination: {
    total: number;
    count: number;
    per_page: number;
    current_page: number;
    total_pages: number;
  };
};

type Portfolio = {
  id: number;
  title: string;
  descriptions: Description[];
};

type Description = {
  id: number;
  portfolio_id: number;
  description: string;
  order: number;
  media: Media[];
};

type Media = {
  id: number;
  url: string;
  type: string; // e.g., "image"
};

type PortfolioFormData = {
  title: string;
  descriptions: {
    description: string;
    order: string; // string because it’s coming from form input
    images: File[]; // array of files like images
  }[];
};
// Create a hook to use the Portfolio API
export const usePortfolioApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Portfolio with pagination
  const useGetPortfolios = (page: number = 1, per_page: number = 10) => {
    return apiServices.useGetListService<PortfolioResponse>({
      url: ProductEndpoints.list,
      params: {
        page: page.toString(),
        per_page: per_page.toString(),
      },
    });
  };

  // Get a single Product by ID
  const useGetPortfolio = (id: number) => {
    return apiServices.useGetItemService<PortfolioItem>({
      url: ProductEndpoints.details,
      id: id.toString(),
      queryOptions: {
        enabled: id > 0, // Only fetch if we have a valid ID
      },
    });
  };

  // Create a new Product
  const useCreateProduct = (onSuccess?: (data: Product) => void) => {
    return apiServices.usePostService<PortfolioFormData, any>({
      url: ProductEndpoints.list,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a Product using PATCH
  const useUpdateProduct = (id: number, onSuccess?: () => void) => {
    return apiServices.usePostService<
      Partial<PortfolioFormData> | { is_home: boolean },
      any
    >({
      url: `${ProductEndpoints.details}/${id.toString()}`,
      // id: id.toString(),
      onSuccess,
      withFormData: false,
      urlAfterSuccess: ProductEndpoints.details + "list",
    });
  };

  // Delete a Product
  const useDeleteProduct = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: ProductEndpoints.details,
      urlAfterSuccess: ProductEndpoints.list + "list",
      onSuccess,
    });
  };

  return {
    useGetPortfolios,
    useGetPortfolio,
    useCreateProduct,
    useUpdateProduct,
    useDeleteProduct,
  };
};
