// The structure for a single item within a portfolio
export interface PortfolioItem {
  text: string;
  // This type supports both existing image URLs (string) and new file uploads (File)
  images: (File | string)[]; 
}

// The main structure for a portfolio entry
export interface Portfolio {
  id: string;
  name: string;
  items: PortfolioItem[];
}

// The data structure specifically for react-hook-form
export interface PortfolioFormData {
  name: string;
  items: {
    text: string;
    images: FileList; // The form uses FileList for file inputs
  }[];
}