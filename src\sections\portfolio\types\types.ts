// Media structure from API response
export interface Media {
  id: number;
  url: string;
  type: string; // e.g., "image"
}

// Description structure from API response
export interface Description {
  id: number;
  portfolio_id: number;
  description: string;
  order: number;
  media: Media[];
}

// The main structure for a portfolio entry (API response)
export interface Portfolio {
  id: number;
  title: string;
  descriptions: Description[];
}

// The data structure specifically for react-hook-form
export interface PortfolioFormData {
  title: string;
  descriptions: {
    description: string;
    order: string; // string because it's coming from form input
    images: File[]; // array of files like images
  }[];
}

// Legacy interface for backward compatibility (if needed)
export interface PortfolioItem {
  text: string;
  images: (File | string)[];
}

// Legacy Portfolio interface for backward compatibility (if needed)
export interface LegacyPortfolio {
  id: string;
  name: string;
  items: PortfolioItem[];
}
