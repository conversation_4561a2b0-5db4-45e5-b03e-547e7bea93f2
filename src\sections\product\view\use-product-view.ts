import { useState, useCallback, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { Product } from "../../type/types/types";
import {
  ProductEndpoints,
  useProductsApi,
} from "../../../services/api/use-product-api";
import { useParams } from "react-router-dom";

// Simulated API function for the toggle

export const useProductView = () => {
  const queryClient = useQueryClient();
  const [expandedProductId, setExpandedProductId] = useState<string | null>(
    null
  );

  // State for Delete Modal (now holds the full product object)
  const [isDeleteModalOpen, setDeleteModalOpen] = useState(false);
  const [productForDelete, setProductForDelete] = useState<Product | undefined>(
    undefined
  );

  // State for "Show on Home" Cover Toggle Modal
  const [isCoverModalOpen, setCoverModalOpen] = useState(false);
  const [productForCover, setProductForCover] = useState<Product | undefined>(
    undefined
  );

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Initialize API hooks
  const { useGetProducts, useDeleteProduct, useUpdateProduct } =
    useProductsApi();

  // Queries with server-side pagination
  const {
    data: productsResponse,
    isLoading,
    error,
  } = useGetProducts(currentPage, itemsPerPage);
  const products = productsResponse?.data;
  const pagination = productsResponse?.pagination;

  // Server-side pagination info
  const totalItems = pagination?.total || 0;
  const totalPages = pagination?.total_pages || 1;

  // Mutations
  const { mutate: deleteProduct, isPending: isDeleting } = useDeleteProduct(
    () => {
      setDeleteModalOpen(false);
      setProductForDelete(undefined);
    }
  );
  const { mutate: updateCover, isPending: isUpdatingCover } = useUpdateProduct(
    +productForCover?.id!
  );

  // Handlers
  const handleToggleExpand = (productId: string) => {
    setExpandedProductId((prevId) => (prevId === productId ? null : productId));
  };

  const openDeleteModal = (e: React.MouseEvent, product: Product) => {
    e.stopPropagation();
    setProductForDelete(product);
    setDeleteModalOpen(true);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setProductForDelete(undefined);
  };

  const handleConfirmDelete = () => {
    if (productForDelete) {
      deleteProduct(parseInt(productForDelete.id));
    }
  };

  const openCoverModal = (e: React.MouseEvent, product: Product) => {
    e.stopPropagation();
    setProductForCover(product);
    setCoverModalOpen(true);
  };

  const closeCoverModal = () => {
    setCoverModalOpen(false);
    setProductForCover(undefined);
  };

  const handleConfirmCoverToggle = () => {
    const formData = new FormData();
    console.log("productForCover", productForCover);
    formData.append("is_home", productForCover?.is_home ? "0" : "1");
    formData.append("_method", "patch");
    if (productForCover) {
      updateCover(formData, {
        onSuccess: () => {
          queryClient.resetQueries({
            queryKey: [ProductEndpoints.list + "list"],
          });
          closeCoverModal();
        },
      });
    }
  };

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // Close any expanded product when changing pages
      setExpandedProductId(null);
    }
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    // Reset to first page when changing items per page
    setCurrentPage(1);
    // Close any expanded product
    setExpandedProductId(null);
  };

  return {
    products,
    isLoading,
    error,
    expandedProductId,
    handleToggleExpand,
    isDeleteModalOpen,
    isDeleting,
    selectedProduct: productForDelete,
    openDeleteModal,
    closeDeleteModal,
    handleConfirmDelete,
    isCoverModalOpen,
    isUpdatingCover,
    productForCover,
    openCoverModal,
    closeCoverModal,
    handleConfirmCoverToggle,
    // Pagination props
    currentPage,
    totalPages,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    totalItems,
  };
};
