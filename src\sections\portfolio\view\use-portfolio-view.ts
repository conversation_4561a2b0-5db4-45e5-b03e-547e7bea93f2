import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { Portfolio } from "../types/types";
import { mockPortfolios } from "../../../mock-data/mockData"; // Adjust path if needed

// Data fetching logic moved from PortfolioPage.tsx
const fetchPortfolios = async (): Promise<Portfolio[]> => {
  console.log("Simulating API call for portfolios...");
  await new Promise((resolve) => setTimeout(resolve, 300));
  return Promise.resolve(mockPortfolios);
};

// Simulated delete logic
const deletePortfolio = async (id: string) => {
    console.log(`Simulating delete for portfolio ID: ${id}`);
    await new Promise((resolve) => setTimeout(resolve, 500));
    // In a real app, you would remove the item from the mock data or call an API
    return { success: true };
}

export function usePortfolioView() {
  const queryClient = useQueryClient();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  const { data: portfolios, isLoading, error } = useQuery({
    queryKey: ["portfolios"],
    queryFn: fetchPortfolios,
  });

  const { mutate: removePortfolio, isPending: isDeleting } = useMutation({
      mutationFn: deletePortfolio,
      onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["portfolios"] });
          closeModal();
      }
  })

  const openModal = (id: string) => {
    setSelectedId(id);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setSelectedId(null);
    setIsModalOpen(false);
  };

  const handleDelete = () => {
    if (selectedId) {
      removePortfolio(selectedId);
    }
  };

  return {
    portfolios,
    isLoading,
    error,
    isModalOpen,
    isDeleting,
    openModal,
    closeModal,
    handleDelete
  };
}