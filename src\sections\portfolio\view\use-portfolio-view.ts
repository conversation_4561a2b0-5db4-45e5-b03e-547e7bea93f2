import { useState } from "react";
import { usePortfolioApi } from "../../../services/api/use-portfolio-api";
import type { Portfolio } from "../types/types";

export function usePortfolioView() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage] = useState(10);

  const { useGetPortfolios, useDeletePortfolio } = usePortfolioApi();

  // Get portfolios with pagination
  const {
    data: portfoliosResponse,
    isLoading,
    error,
  } = useGetPortfolios(currentPage, perPage);

  // Delete portfolio mutation
  const { mutate: removePortfolio, isPending: isDeleting } = useDeletePortfolio(
    () => {
      closeModal();
    }
  );

  const openModal = (id: number) => {
    setSelectedId(id);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setSelectedId(null);
    setIsModalOpen(false);
  };

  const handleDelete = () => {
    if (selectedId) {
      removePortfolio(selectedId);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return {
    portfolios: portfoliosResponse?.data || [],
    pagination: portfoliosResponse?.pagination,
    isLoading,
    error,
    isModalOpen,
    isDeleting,
    currentPage,
    openModal,
    closeModal,
    handleDelete,
    handlePageChange,
  };
}
