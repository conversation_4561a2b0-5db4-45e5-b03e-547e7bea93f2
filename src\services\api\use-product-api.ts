import useApiServices from "../hooks/use-api-services";

import axiosInstance from "../../utils/axios";
import type { Product } from "../../sections/type/types/types";
// Define the API endpoints for Products
export const ProductEndpoints = {
  list: "/products",
  details: "/products",
};

// Define the API response types
export interface ProductsListResponse {
  data: Product[];
  pagination: {
    total: number;
    count: number;
    per_page: number;
    current_page: number;
    total_pages: number;
  };
}

// Single product response type to match the actual API response
export interface ProductResponse {
  status: string;
  data: {
    id: number;
    type: {
      id: number;
      name: string;
    };
    name: string;
    slug: string;
    is_home: boolean;
    description: string;
    mini_description: string;
    categories: string;
    tags: string;
    images: Array<{
      id: number;
      url: string;
      type: string;
    }>;
    applications: Array<{
      id: number;
      title: string;
      media: Array<{
        id: number;
        url: string;
        type: string;
      }>;
    }>;
    key_benefits: Array<{
      id: number;
      title: string;
      media: {
        id: number;
        url: string;
        type: string;
      };
    }>;
    specifications: Array<{
      id: number;
      title: string;
      content: string;
    }>;
    video: string;
  };
  message: string;
}

// Define the request body types for FormData
// Since we're using withFormData: true, the actual request will be FormData
// This type represents the structure that gets converted to FormData
export type CreateProductBody = {
  name: string;
  description: string;
  mini_description: string;
  type_id: string;
  categories: string;
  tags: string;
  images?: File[];
  video_file?: File;
  specifications?: {
    title: string;
    content: string;
  }[];
  applications?: {
    title: string;
    images?: File[];
  }[];
  key_benefits?: {
    title: string;
    images?: File[];
  }[];
  is_home?: boolean;
};

// Create a hook to use the Products API
export const useProductsApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Products with pagination
  const useGetProducts = (page: number = 1, per_page: number = 10) => {
    return apiServices.useGetListService<ProductsListResponse>({
      url: ProductEndpoints.list,
      params: {
        page: page.toString(),
        per_page: per_page.toString(),
      },
    });
  };

  // Get a single Product by ID
  const useGetProduct = (id: number) => {
    return apiServices.useGetItemService<ProductResponse>({
      url: ProductEndpoints.details,
      id: id.toString(),
      queryOptions: {
        enabled: id > 0, // Only fetch if we have a valid ID
      },
    });
  };

  // Create a new Product
  const useCreateProduct = (onSuccess?: (data: Product) => void) => {
    return apiServices.usePostService<FormData, Product>({
      url: ProductEndpoints.list,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a Product using PATCH
  const useUpdateProduct = (id: number, onSuccess?: () => void) => {
    return apiServices.usePostService<
      Partial<FormData> | { is_home: boolean },
      Product
    >({
      url: `${ProductEndpoints.details}/${id.toString()}`,
      // id: id.toString(),
      onSuccess,
      withFormData: false,
      urlAfterSuccess: ProductEndpoints.details + "list",
    });
  };

  // Delete a Product
  const useDeleteProduct = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: ProductEndpoints.details,
      urlAfterSuccess: ProductEndpoints.list + "list",
      onSuccess,
    });
  };

  return {
    useGetProducts,
    useGetProduct,
    useCreateProduct,
    useUpdateProduct,
    useDeleteProduct,
  };
};
