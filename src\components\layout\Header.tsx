import { useNavigate } from 'react-router-dom';

const Header = () => {
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem('authToken');
    navigate('/');
  };

  return (
    <header className="flex items-center justify-between h-16 px-6 bg-white shadow-lg">
      <div />
      <div>
        <button
          onClick={handleLogout}
          className="px-4 py-2 font-semibold text-secondary-01 bg-gray-100 rounded-md hover:bg-yellow-100 cursor-pointer"
        >
          Logout
        </button>
      </div>
    </header>
  );
};

export default Header;