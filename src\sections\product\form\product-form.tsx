import React, { useEffect } from "react";
import { useFormContext, useFieldArray, useWatch } from "react-hook-form";
import type { Type } from "../../type/types/types";
import type { ProductFormData } from "../types/types";
import type { ProductResponse } from "../../../services/api/use-product-api";
import type { SingleTypeResponse } from "../../../services/api/use-type-api";

export const ProductForm: React.FC<{
  productToEdit: ProductResponse;
  types: any;
  isEditMode?: boolean;
  existingImages?: any[];
  existingApplicationImages?: any[];
  existingKeyBenefitImages?: any[];
}> = ({
  productToEdit,
  types,
  isEditMode,
  existingImages,
  existingApplicationImages,
  existingKeyBenefitImages,
}) => {
  const {
    register,
    formState: { errors },
    watch,
  } = useFormContext<ProductFormData>();

  // Watch the images and video fields to show preview
  const watchedImages = watch("images");
  const watchedVideoFile = watch("video_file");
  return (
    <div className="space-y-8 bg-white p-6 md:p-8 rounded-lg shadow-md">
      {/* Basic Info */}
      <div className="border-b pb-6">
        <h2 className="text-xl font-semibold text-secondary-01">
          Basic Information
        </h2>
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="form-label">Product Name *</label>
            <input
              {...register("name", {
                required: "Product name is required",
                minLength: {
                  value: 2,
                  message: "Name must be at least 2 characters",
                },
              })}
              className="input-style"
              placeholder="Enter product name"
            />
            {errors.name && (
              <p className="error-message">{errors.name?.message}</p>
            )}
          </div>
          <div>
            <label className="form-label">Type *</label>
            <select
              {...register("type_id", {
                required: "Product type is required",
                // convert value to number if needed
                valueAsNumber: false, // if t.id is a number
              })}
              className="input-style"
              defaultValue="" // ensure empty initial value
            >
              <option value="" disabled>
                Select a type
              </option>
              {types?.map((t: any) => (
                <option key={t.id} value={String(t.id)}>
                  {t.name}
                </option>
              ))}
            </select>
            {errors.type_id && (
              <p className="error-message">{errors.type_id?.message}</p>
            )}
          </div>

          <div>
            <label className="form-label">Categories *</label>
            <input
              {...register("categories", {
                required: "Categories field is required",
              })}
              placeholder="e.g., Conveyors, Automation"
              className="input-style"
            />
            {errors.categories && (
              <p className="error-message">{errors.categories?.message}</p>
            )}
          </div>
          <div className="md:col-span-2">
            <label className="form-label">Tags *</label>
            <input
              {...register("tags", { required: "Tags field is required" })}
              placeholder="e.g., high-speed, logistics"
              className="input-style"
            />
            {errors.tags && (
              <p className="error-message">{errors.tags?.message}</p>
            )}
          </div>
          <div className="md:col-span-2">
            <label className="form-label">Mini Description *</label>
            <textarea
              {...register("mini_description", {
                required: "Mini description is required",
                minLength: {
                  value: 10,
                  message: "Mini description must be at least 10 characters",
                },
              })}
              rows={2}
              className="input-style"
              placeholder="Enter a brief description of the product"
            />
            {errors.mini_description && (
              <p className="error-message">
                {errors.mini_description?.message}
              </p>
            )}
          </div>
          <div className="md:col-span-2">
            <label className="form-label">Full Description *</label>
            <textarea
              {...register("description", {
                required: "Full description is required",
                minLength: {
                  value: 20,
                  message: "Description must be at least 20 characters",
                },
              })}
              rows={4}
              className="input-style"
              placeholder="Enter a detailed description of the product"
            />
            {errors.description && (
              <p className="error-message">{errors.description?.message}</p>
            )}
          </div>
        </div>
      </div>
      {/* Media */}
      <div className="border-b pb-6">
        <h2 className="text-xl font-semibold text-secondary-01">Media</h2>
        <div className="mt-4 space-y-6">
          <div>
            <label className="form-label">Main Images</label>

            {/* Show existing images in edit mode */}
            {isEditMode && existingImages && existingImages.length > 0 && (
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">Current images:</p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {existingImages.map((image: any, index: number) => (
                    <div key={image.id || index} className="relative">
                      <img
                        src={image.url || image.path}
                        alt={`Current product image ${index + 1}`}
                        className="w-full h-32 object-cover rounded-md border"
                      />
                      <div className="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded">
                        {index + 1}
                      </div>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Upload new images to replace the current ones
                </p>
              </div>
            )}

            <input
              type="file"
              {...register("images")}
              multiple
              accept="image/*"
              className="input-style"
            />
            {errors.images && (
              <p className="text-red-500 text-sm mt-1">
                {errors.images?.message}
              </p>
            )}

            {/* Show preview of new selected images */}
            {watchedImages && watchedImages.length > 0 && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">
                  New images preview:
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Array.from(watchedImages).map(
                    (image: any, index: number) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`New image preview ${index + 1}`}
                          className="w-full h-32 object-cover rounded-md border"
                        />
                        <div className="absolute top-1 right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded">
                          New {index + 1}
                        </div>
                      </div>
                    )
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Video File Section */}
          <div>
            <label className="form-label">Product Video</label>

            {/* Show existing video in edit mode */}
            {isEditMode && productToEdit?.data?.video && (
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">Current video:</p>
                <div className="relative max-w-md">
                  <video
                    src={productToEdit?.data?.video}
                    className="w-full h-48 object-cover rounded-md border"
                    controls
                  />
                  <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                    Current Video
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Upload a new video to replace the current one
                </p>
              </div>
            )}

            <input
              type="file"
              {...register("video_file")}
              accept="video/*"
              className="input-style"
            />
            {errors.video_file && (
              <p className="text-red-500 text-sm mt-1">
                {errors.video_file?.message}
              </p>
            )}

            {/* Show preview of new selected video */}
            {watchedVideoFile && watchedVideoFile.length > 0 && (
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">New video preview:</p>
                <div className="relative max-w-md">
                  <video
                    src={URL.createObjectURL(watchedVideoFile[0])}
                    className="w-full h-48 object-cover rounded-md border"
                    controls
                  />
                  <div className="absolute top-2 left-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                    New Video
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <ListSection
        sectionName="applications"
        title="Applications"
        placeholder="e.g., Assembly Lines"
        isEditMode={isEditMode}
        existingImages={existingApplicationImages}
      />
      <ListSection
        sectionName="key_benefits"
        title="Key Benefits"
        placeholder="e.g., Low noise during operating"
        isEditMode={isEditMode}
        existingImages={existingKeyBenefitImages}
      />
      <SpecificationsSection />
    </div>
  );
};

// Reusable component for list-based sections (Applications, Benefits)
function ListSection({
  sectionName,
  title,
  placeholder,
  isEditMode,
  existingImages,
}: {
  sectionName: "applications" | "key_benefits";
  title: string;
  placeholder: string;
  isEditMode?: boolean;
  existingImages?: any[];
}) {
  const { control, register, setValue, watch } =
    useFormContext<ProductFormData>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: `${sectionName}.items`,
  });

  // Watch the items array to enable/disable image upload
  const items = useWatch({ control, name: `${sectionName}.items` });
  const imageUploadEnabled = !!items && items.length > 0;

  // Watch the images field to show preview
  const watchedImages = watch(`${sectionName}.images`);

  // Effect to clear images if the last item is removed
  useEffect(() => {
    if (!imageUploadEnabled) {
      setValue(`${sectionName}.images`, new DataTransfer().files);
    }
  }, [imageUploadEnabled, sectionName, setValue]);

  return (
    <div className="border-b pb-8">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-secondary-01">{title}</h3>
        <button
          type="button"
          onClick={() => append({ value: "" })}
          className="btn-secondary text-sm"
        >
          + Add Item
        </button>
      </div>
      <div className="space-y-3">
        {fields.map((field, index) => (
          <div
            key={field.id}
            className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50"
          >
            <input
              {...register(`${sectionName}.items.${index}.value` as const)}
              placeholder={placeholder}
              className="input-style flex-grow"
            />
            <button
              type="button"
              onClick={() => remove(index)}
              className="text-red-500 hover:text-red-700 text-2xl font-bold p-1"
            >
              ×
            </button>
          </div>
        ))}
      </div>
      <div className="mt-4">
        <label
          className={`form-label ${!imageUploadEnabled ? "text-gray-400" : ""}`}
        >
          Section Images
        </label>

        {/* Show existing images in edit mode */}
        {isEditMode && existingImages && existingImages.length > 0 && (
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">Current images:</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {existingImages.map((image: any, index: number) => (
                <div key={image.id || index} className="relative">
                  <img
                    src={image.url || image.path}
                    alt={`Current ${sectionName} image ${index + 1}`}
                    className="w-full h-32 object-cover rounded-md border"
                  />
                  <div className="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 py-0.5 rounded">
                    {index + 1}
                  </div>
                </div>
              ))}
            </div>
            <p className="text-xs text-gray-500 mt-2">
              Upload new images to replace the current ones
            </p>
          </div>
        )}

        <input
          type="file"
          {...register(`${sectionName}.images` as const)}
          multiple
          accept="image/*"
          className="input-style"
          disabled={!imageUploadEnabled}
        />

        {/* Show preview of new selected images */}
        {watchedImages && watchedImages.length > 0 && (
          <div className="mt-4">
            <p className="text-sm text-gray-600 mb-2">New images preview:</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {Array.from(watchedImages).map((image: any, index: number) => (
                <div key={index} className="relative">
                  <img
                    src={URL.createObjectURL(image)}
                    alt={`New ${sectionName} image preview ${index + 1}`}
                    className="w-full h-32 object-cover rounded-md border"
                  />
                  <div className="absolute top-1 right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded">
                    New {index + 1}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {!imageUploadEnabled && (
          <p className="text-xs text-gray-500 mt-1">
            Add at least one item to enable image uploads.
          </p>
        )}
      </div>
    </div>
  );
}

// Specific component for Specifications Section
function SpecificationsSection() {
  const { control, register, setValue, watch } =
    useFormContext<ProductFormData>();
  const { fields, append, remove } = useFieldArray({
    control,
    name: "specifications.items",
  });

  return (
    <div className="pb-8">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold text-secondary-01">
          Specifications
        </h3>
        <button
          type="button"
          onClick={() => append({ title: "", content: "" })}
          className="btn-secondary text-sm"
        >
          + Add Spec
        </button>
      </div>
      <div className="space-y-4">
        {fields.map((field, index) => (
          <div
            key={field.id}
            className="p-4 border rounded-lg bg-gray-50 relative"
          >
            <button
              type="button"
              onClick={() => remove(index)}
              className="absolute top-2 right-2 text-red-500 hover:text-red-700 text-2xl font-bold p-1"
            >
              ×
            </button>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="form-label text-sm">Title</label>
                <input
                  {...register(`specifications.items.${index}.title` as const)}
                  placeholder="e.g., Pallet Width"
                  className="input-style"
                />
              </div>
              <div>
                <label className="form-label text-sm">Content</label>
                <input
                  {...register(
                    `specifications.items.${index}.content` as const
                  )}
                  placeholder="e.g., 160, 240, 320"
                  className="input-style"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
