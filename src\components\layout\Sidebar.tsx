import { NavLink } from 'react-router-dom';

const navigation = [
  { name: 'Messages', path: '/dashboard/messages' },
  { name: 'types', path: '/dashboard/types' },
  { name: 'Products', path: '/dashboard/products' },
  { name: 'Portfolios', path: '/dashboard/portfolios' },
];

const Sidebar = () => {
  return (
    <aside className="w-64 flex-shrink-0 bg-white  border-primary-01/50">
      <div className="flex items-center justify-center h-16">
        <h1 className="text-2xl font-bold text-primary-01 border-b pb-5 border-gray-200 ">Admin Dashboard</h1>
      </div>
      <nav className="mt-6">
        {navigation.map((item) => (
          <NavLink
            key={item.name}
            to={item.path}
            className={({ isActive }) =>
              `flex items-center px-6 py-3 font-medium border-r-4 
            hover:bg-yellow-100 hover:text-primary-01 hover:border-primary-01 transition-colors duration-200 ${
                isActive ? 'bg-yellow-100 text-primary-01 border-r-4 border-primary-01' : 'border-transparent text-secondary-01 '
              }`
            }
          >
            {item.name}
          </NavLink>
        ))}
      </nav>
    </aside>
  );
};

export default Sidebar;