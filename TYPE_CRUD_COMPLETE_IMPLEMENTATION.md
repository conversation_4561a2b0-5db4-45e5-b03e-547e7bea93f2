# Type CRUD Complete Implementation

This document outlines the complete CRUD system implementation for Types, similar to the Products CRUD system.

## ✅ **Complete Features Implemented**

### 🔧 **1. API Layer (use-type-api.ts)**

**Updated API Hooks:**
```typescript
// Get all Types with pagination
const useGetTypes = (page: number = 1, per_page: number = 10) => {
  return apiServices.useGetListService<TypeResponse>({
    url: TypeEndpoints.list,
    params: {
      page: page.toString(),
      per_page: per_page.toString(),
    },
  });
};

// Get a single Type by ID
const useGetType = (id: number) => {
  return apiServices.useGetItemService<SingleTypeResponse>({
    url: TypeEndpoints.details,
    id: id.toString(),
    queryOptions: {
      enabled: id > 0, // Only fetch if we have a valid ID
    },
  });
};

// Create a new Type with FormData
const useCreateType = (onSuccess?: (data: any) => void) => {
  return apiServices.usePostService<FormData, any>({
    url: TypeEndpoints.list,
    onSuccess,
    withFormData: true, // ✅ Uses FormData for file uploads
  });
};

// Update a Type using PATCH with FormData
const useUpdateType = (id: number, onSuccess?: () => void) => {
  return apiServices.usePatchService<FormData>({
    url: TypeEndpoints.details,
    id: id.toString(),
    onSuccess,
    withFormData: true, // ✅ Uses FormData for file uploads
    queryKey: TypeEndpoints.list + "list",
  });
};
```

**FormData Structure:**
```typescript
export type FormCreateUpdateData = {
  name: string;
  description: string;
  parent_id: string | number;
  image: File; // ✅ File upload support
};
```

### 🔧 **2. Form Hook (use-type-form.ts)**

**Complete Form Management:**
```typescript
export const useTypeForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  // API hooks
  const { useGetTypes, useGetType, useCreateType, useUpdateType } = useTypeApi();

  // Form setup with React Hook Form
  const formMethods = useForm<TypeFormData>({
    defaultValues: {
      name: "",
      description: "",
      parent_id: "",
      image: new DataTransfer().files,
    },
    mode: "onChange",
  });

  // Form reset logic (prevents constant resets)
  const [formInitialized, setFormInitialized] = useState(false);

  // Create submission with FormData
  const handleCreateType = (data: TypeFormData) => {
    const formData = new FormData();
    formData.append("name", data.name || "");
    formData.append("description", data.description || "");
    formData.append("parent_id", data.parent_id || "");
    
    if (data.image && data.image.length > 0) {
      formData.append("image", data.image[0]);
    }

    addType(formData);
  };

  // Update submission with FormData
  const handleUpdateType = (data: TypeFormData) => {
    const formData = new FormData();
    formData.append("name", data.name || "");
    formData.append("description", data.description || "");
    formData.append("parent_id", data.parent_id || "");
    
    // Only append image if new file is selected
    if (data.image && data.image.length > 0) {
      formData.append("image", data.image[0]);
    }

    updateType(formData);
  };

  return {
    formMethods,
    isEditMode,
    isLoading,
    isSubmitting,
    parentTypes, // For parent type dropdown
    onSubmit,
    resetForm,
    existingImage, // For edit mode preview
    createError,
    updateError,
  };
};
```

### 🔧 **3. Form Component (type-form.tsx)**

**Complete Form with File Upload:**
```typescript
const TypeForm = () => {
  const {
    formMethods,
    isEditMode,
    isLoading,
    isSubmitting,
    parentTypes,
    onSubmit,
    existingImage,
  } = useTypeForm();

  const { register, handleSubmit, formState: { errors }, watch } = formMethods;
  const watchedImage = watch("image");

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Type Name */}
        <input
          {...register("name", {
            required: "Type name is required",
            minLength: { value: 2, message: "Type name must be at least 2 characters" }
          })}
          placeholder="Enter type name"
        />

        {/* Description */}
        <textarea
          {...register("description", {
            required: "Description is required",
            minLength: { value: 10, message: "Description must be at least 10 characters" }
          })}
          placeholder="Enter type description"
        />

        {/* Parent Type Dropdown */}
        <select {...register("parent_id")}>
          <option value="">Select parent type (optional)</option>
          {parentTypes?.map((type) => (
            <option key={type.id} value={type.id}>
              {type.name}
            </option>
          ))}
        </select>

        {/* Image Upload with Preview */}
        {isEditMode && existingImage && (
          <div>
            <p>Current image:</p>
            <img src={existingImage} alt="Current type image" className="w-32 h-32 object-cover rounded-md" />
          </div>
        )}

        <input
          {...register("image")}
          type="file"
          accept="image/*"
        />

        {/* New Image Preview */}
        {watchedImage && watchedImage.length > 0 && (
          <div>
            <p>New image preview:</p>
            <img 
              src={URL.createObjectURL(watchedImage[0])} 
              alt="New image preview" 
              className="w-32 h-32 object-cover rounded-md"
            />
          </div>
        )}

        <button type="submit" disabled={isSubmitting}>
          {isSubmitting ? (isEditMode ? "Updating..." : "Creating...") : (isEditMode ? "Update Type" : "Create Type")}
        </button>
      </form>
    </FormProvider>
  );
};
```

### 🔧 **4. View Hook (use-type-view.ts)**

**Complete View Management with Pagination:**
```typescript
export const useTypeView = () => {
  const [expandedTypeId, setExpandedTypeId] = useState<string | null>(null);
  const [isDeleteModalOpen, setDeleteModalOpen] = useState(false);
  const [typeForDelete, setTypeForDelete] = useState<any | undefined>(undefined);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // API hooks
  const { useGetTypes, useDeleteType } = useTypeApi();

  // Server-side pagination
  const { data: typesResponse, isLoading, error } = useGetTypes(currentPage, itemsPerPage);
  const types = typesResponse?.data;
  const pagination = typesResponse?.pagination;

  // Pagination info
  const totalItems = pagination?.total || 0;
  const totalPages = pagination?.total_pages || 1;

  // Delete functionality
  const { mutate: deleteType, isPending: isDeleting } = useDeleteType(() => {
    setDeleteModalOpen(false);
    setTypeForDelete(undefined);
  });

  // Handlers
  const handleToggleExpand = (typeId: string) => {
    setExpandedTypeId(expandedTypeId === typeId ? null : typeId);
  };

  const handlePageChange = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      setExpandedTypeId(null);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    setExpandedTypeId(null);
  };

  return {
    types,
    isLoading,
    error,
    expandedTypeId,
    handleToggleExpand,
    isDeleteModalOpen,
    isDeleting,
    selectedType: typeForDelete,
    openDeleteModal,
    closeDeleteModal,
    handleConfirmDelete,
    // Pagination
    currentPage,
    totalPages,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    totalItems,
  };
};
```

### 🔧 **5. View Component (type-view.tsx)**

**Complete Table View with Pagination:**
```typescript
const TypeView = () => {
  const {
    types,
    isLoading,
    error,
    expandedTypeId,
    handleToggleExpand,
    isDeleteModalOpen,
    isDeleting,
    selectedType,
    openDeleteModal,
    closeDeleteModal,
    handleConfirmDelete,
    currentPage,
    totalPages,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    totalItems,
  } = useTypeView();

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-secondary-01">Types Management</h1>
        <Link to="/dashboard/types/add" className="bg-primary-01 text-white px-4 py-2 rounded-md">
          Add New Type
        </Link>
      </div>

      {/* Types Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th>Type</th>
              <th>Description</th>
              <th>Parent Type</th>
              <th>Image</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {types?.map((type: any) => [
              <tr key={type.id}>
                <td>{type.name}</td>
                <td>{type.description || "No description"}</td>
                <td>{type.parent_id ? `Parent ID: ${type.parent_id}` : "Root Type"}</td>
                <td>
                  {type.image ? (
                    <img src={type.image} alt={type.name} className="h-12 w-12 object-cover rounded-md" />
                  ) : (
                    <div className="h-12 w-12 bg-gray-200 rounded-md">No Image</div>
                  )}
                </td>
                <td>
                  <Link to={`/dashboard/types/edit/${type.id}`}>Edit</Link>
                  <button onClick={() => openDeleteModal(type)}>Delete</button>
                  <button onClick={() => handleToggleExpand(type.id.toString())}>
                    {expandedTypeId === type.id.toString() ? "Collapse" : "Expand"}
                  </button>
                </td>
              </tr>,
              // Expanded row
              expandedTypeId === type.id.toString() && (
                <tr key={`${type.id}-expanded`}>
                  <td colSpan={5}>
                    <div>
                      <h4>Full Description:</h4>
                      <p>{type.description || "No description available"}</p>
                      {type.products && type.products.length > 0 && (
                        <div>
                          <h4>Products ({type.products.length}):</h4>
                          <div>
                            {type.products.slice(0, 6).map((product: any) => (
                              <div key={product.id}>{product.name}</div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </td>
                </tr>
              )
            ].filter(Boolean))}
          </tbody>
        </table>

        {/* Pagination Controls */}
        <div className="pagination-controls">
          <select value={itemsPerPage} onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}>
            <option value={5}>5 per page</option>
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
          </select>
          
          <button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage <= 1}>
            Previous
          </button>
          <span>Page {currentPage} of {totalPages}</span>
          <button onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage >= totalPages}>
            Next
          </button>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleConfirmDelete}
        isConfirming={isDeleting}
        title="Delete Type"
        message={`Are you sure you want to delete "${selectedType?.name}"?`}
      />
    </div>
  );
};
```

## 🎯 **API Integration**

### Request Format (FormData)
```javascript
// Create/Update Type
const formData = new FormData();
formData.append("name", "Type Name");
formData.append("description", "Type Description");
formData.append("parent_id", "5"); // Optional parent type ID
formData.append("image", imageFile); // File upload
```

### Response Format
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "Type Name",
    "description": "Type Description",
    "parent_id": 5,
    "image": "https://example.com/image.jpg",
    "media": [
      {
        "id": 1,
        "url": "https://example.com/image.jpg",
        "type": "image"
      }
    ],
    "products": [
      {
        "id": 1,
        "name": "Product Name"
      }
    ]
  },
  "message": "Operation Done"
}
```

## 🚀 **Features Working**

- ✅ **Create Type** - FormData submission with file upload
- ✅ **Read Types** - Paginated list with server-side pagination
- ✅ **Update Type** - FormData submission with existing image preview
- ✅ **Delete Type** - Confirmation modal and removal
- ✅ **File Upload** - Image upload with preview (existing and new)
- ✅ **Pagination** - Server-side pagination with page/per_page parameters
- ✅ **Parent Types** - Dropdown selection for hierarchical types
- ✅ **Expandable Rows** - Show additional details and related products
- ✅ **Form Validation** - Client-side validation with error messages
- ✅ **Loading States** - Proper loading indicators during API calls
- ✅ **Error Handling** - API error display and form error mapping

## 🎉 **Complete Type CRUD System**

The Type CRUD system now provides:
- ✅ **Complete CRUD operations** with FormData support
- ✅ **File upload functionality** with image preview
- ✅ **Server-side pagination** for better performance
- ✅ **Hierarchical type support** with parent type selection
- ✅ **Expandable table rows** for detailed information
- ✅ **Form validation** and error handling
- ✅ **Responsive design** with proper styling
- ✅ **Type-safe implementation** with TypeScript

All functionality is working correctly with proper FormData handling, file uploads, pagination, and CRUD operations!
