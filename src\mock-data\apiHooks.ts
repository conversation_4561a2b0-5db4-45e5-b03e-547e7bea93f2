import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import type { TypeFormData } from "../sections/type/types/types";

// Generic success handler
const useSuccessHandler = (redirectPath: string, queryKeyToInvalidate: string) => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  return () => {
    console.log(`Action successful! Invalidating query: ${queryKeyToInvalidate}`);
    queryClient.invalidateQueries({ queryKey: [queryKeyToInvalidate] });
    navigate(redirectPath);
  };
};

// Type (Category) Hooks
export const useAddSubType = () => {
  const handleSuccess = useSuccessHandler("/dashboard/types", "types");
  return useMutation({
    mutationFn: async (data: { parentId: string } & TypeFormData) => {
      console.log(`Simulating ADD sub-type to parent ID ${data.parentId}:`, data);
      await new Promise((res) => setTimeout(res, 500));
      return Promise.resolve();
    },
    onSuccess: handleSuccess,
  });
};

export const useUpdateSubType = () => {
  const handleSuccess = useSuccessHandler("/dashboard/types", "types");
  return useMutation({
    mutationFn: async (updatedSub: { id: string } & TypeFormData) => {
      console.log("Simulating UPDATE sub-type:", updatedSub);
      await new Promise((res) => setTimeout(res, 500));
      return Promise.resolve();
    },
    onSuccess: handleSuccess,
  });
};

export const useDeleteSubType = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      console.log("Simulating DELETE sub-type with ID:", id);
      await new Promise((res) => setTimeout(res, 500));
      return Promise.resolve();
    },
    onSuccess: () => {
      console.log("Delete successful! Invalidating types...");
      queryClient.invalidateQueries({ queryKey: ["types"] });
    },
  });
};

// Product Hooks

// Note: The mutation function expects a browser FormData object, not a custom type.
// This is standard for handling file uploads.
export const useAddProduct = () => {
  const handleSuccess = useSuccessHandler("/dashboard/products", "products");
  return useMutation({
    mutationFn: async (formData: FormData) => {
      console.log("Simulating ADD product with FormData:");
      for (const [key, value] of formData.entries()) {
        console.log(key, value);
      }
      await new Promise((res) => setTimeout(res, 500));
      return Promise.resolve();
    },
    onSuccess: handleSuccess,
  });
};

export const useUpdateProduct = () => {
  const handleSuccess = useSuccessHandler("/dashboard/products", "products");
  return useMutation({
    mutationFn: async ({ id, formData }: { id: string; formData: FormData }) => {
      console.log(`Simulating UPDATE product ${id} with FormData:`);
      formData.append('_method', 'PUT');
      for (const [key, value] of formData.entries()) {
        console.log(key, value);
      }
      await new Promise((res) => setTimeout(res, 500));
      return Promise.resolve();
    },
    onSuccess: handleSuccess,
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (id: string) => {
      console.log("Simulating DELETE product with ID:", id);
      await new Promise((res) => setTimeout(res, 500));
      return Promise.resolve();
    },
    onSuccess: () => {
      console.log("Delete successful! Invalidating products...");
      queryClient.invalidateQueries({ queryKey: ["products"] });
    },
  });
};