import {
  enqueueSnackbar as notiEnqueueSnackbar,
  closeSnackbar,
  type VariantType,
} from "notistack";

// ----------------------------------------------------------------------

type SnackbarOptions = {
  variant?: VariantType;
  anchorOrigin?: {
    vertical: "top" | "bottom";
    horizontal: "left" | "center" | "right";
  };
};

export const useSnackbar = () => {
  const enqueueSnackbar = (message: string, options?: SnackbarOptions) => {
    const currentLng = localStorage.getItem("i18nextLng") || "en";

    return notiEnqueueSnackbar(message, {
      variant: options?.variant || "default",
      anchorOrigin: options?.anchorOrigin || {
        vertical: "bottom",
        horizontal: currentLng === "en" ? "right" : "left",
      },
    });
  };

  return { enqueueSnackbar, closeSnackbar };
};
