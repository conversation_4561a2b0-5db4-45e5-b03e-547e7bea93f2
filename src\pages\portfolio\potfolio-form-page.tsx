import { FormProvider } from "react-hook-form";
import { usePortfolioForm } from "../../sections/portfolio/form/use-portfolio-form";
import { PortfolioForm } from "../../sections/portfolio/form/portfolio-form";

export default function PortfolioFormPage() {
  const {
    formMethods,
    onSubmit,
    isLoading,
    isSubmitting,
    isEditMode,
    existingPortfolio,
  } = usePortfolioForm();

  if (isLoading) return <div>Loading portfolio details...</div>;

  return (
    <div>
      <h1 className="text-3xl font-bold text-secondary-01 mb-6">
        {isEditMode ? "Edit Portfolio" : "Add New Portfolio"}
      </h1>
      <FormProvider {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)}>
          <PortfolioForm existingPortfolio={existingPortfolio} />
          <div className="max-w-4xl mx-auto mt-8">
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full py-3 btn-primary"
            >
              {isSubmitting ? "Saving..." : "Save Portfolio"}
            </button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}
