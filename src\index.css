@import "tailwindcss";

@layer components {
  .input-style {
    @apply w-full mt-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01;
  }
  .file-input {
    @apply block w-full text-sm text-secondary-01 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-yellow-50 file:text-yellow-700 hover:file:bg-yellow-100;
  }
  .btn-primary {
      @apply w-full py-2 px-4 font-semibold text-white bg-primary-01/90 rounded-md hover:bg-primary-01 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:bg-gray-400;
  }
}
@theme {
  --color-primary-01:#FCC50D;
  --color-secondary-01:#3C3C3B;
  --spacing-main-padding-large: 100px;
}