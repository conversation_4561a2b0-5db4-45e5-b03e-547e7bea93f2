
export const TabButton = ({ label, activeTab, setActiveTab }: { label: string; activeTab: string; setActiveTab: (label: string) => void }) => (
  <button
    onClick={() => setActiveTab(label)}
    className={`px-4 py-2 text-sm md:text-base font-semibold rounded-t-lg transition-colors duration-200 focus:outline-none ${
      activeTab === label
        ? 'bg-yellow-400 text-secondary-01 border-b-4 border-yellow-500'
        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
    }`}
  >
    {label}
  </button>
);