import { useState, useCallback } from "react";
import { useQueryClient } from "@tanstack/react-query";
import useMessagesApi from "../../../services/api/use-messages-api";

// Type for the message from API (matching the API response)
type ContactMessage = {
  id: number;
  name: string;
  phone: string;
  email: string;
  message: string;
  finding_way: string;
};

export function useMessageView() {
  const queryClient = useQueryClient();
  const [expandedMessageId, setExpandedMessageId] = useState<string | null>(
    null
  );

  // State for Delete Modal
  const [isDeleteModalOpen, setDeleteModalOpen] = useState(false);
  const [messageForDelete, setMessageForDelete] = useState<
    ContactMessage | undefined
  >(undefined);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Initialize API hooks
  const { useGetMessages, useDeleteMessage } = useMessagesApi();

  // Queries with server-side pagination
  const {
    data: messagesResponse,
    isLoading,
    error,
  } = useGetMessages(currentPage, itemsPerPage);

  const messages = messagesResponse?.data;
  const pagination = messagesResponse?.pagination;

  // Server-side pagination info
  const totalItems = pagination?.total || 0;
  const totalPages = pagination?.total_pages || 1;

  // Mutations
  const { mutate: deleteMessage, isPending: isDeleting } = useDeleteMessage(
    () => {
      setDeleteModalOpen(false);
      setMessageForDelete(undefined);
      // Invalidate queries to refresh the list
      queryClient.invalidateQueries({ queryKey: ["/user-messages"] });
    }
  );

  // Toggle expanded message
  const handleToggleExpand = (messageId: string) => {
    setExpandedMessageId(expandedMessageId === messageId ? null : messageId);
  };

  // Delete modal handlers
  const openDeleteModal = useCallback((message: ContactMessage) => {
    setMessageForDelete(message);
    setDeleteModalOpen(true);
  }, []);

  const closeDeleteModal = useCallback(() => {
    setDeleteModalOpen(false);
    setMessageForDelete(undefined);
  }, []);

  const handleConfirmDelete = useCallback(() => {
    if (messageForDelete) {
      deleteMessage(messageForDelete.id);
    }
  }, [messageForDelete, deleteMessage]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // Close any expanded message when changing pages
      setExpandedMessageId(null);
    }
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    // Reset to first page when changing items per page
    setCurrentPage(1);
    // Close any expanded message
    setExpandedMessageId(null);
  };

  return {
    messages,
    isLoading,
    error,
    expandedMessageId,
    handleToggleExpand,
    isDeleteModalOpen,
    isDeleting,
    selectedMessage: messageForDelete,
    openDeleteModal,
    closeDeleteModal,
    handleConfirmDelete,
    // Pagination props
    currentPage,
    totalPages,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    totalItems,
    pagination,
  };
}
