export interface MediaItem {
  id: number;
  url: string;
  type: string;
}

export interface SpecificationItem {
  id?: number;
  title: string;
  content: string;
}

export interface ApplicationItem {
  id?: number;
  title: string;
  media?: MediaItem[];
}

export interface KeyBenefitItem {
  id?: number;
  title: string;
  media?: MediaItem;
}

// Legacy interface for form data compatibility
interface SectionWithItems<T> {
  items: T[];
  images: (File | string)[];
}

export interface Product {
  id: number | string;
  name: string;
  slug?: string;
  description: string;
  mini_description: string;
  type?: {
    id: number;
    name: string;
  };
  type_id?: string | number;
  categories: string;
  tags: string;
  is_home?: boolean;
  isCover?: boolean;
  images: MediaItem[] | (File | string)[];
  video?: string;
  video_file?: File | string;
  specifications?: SpecificationItem[];
  applications?: ApplicationItem[];
  key_benefits?: KeyBenefitItem[];
}

export interface ProductFormData {
  name: string;
  slug?: string;
  description: string;
  mini_description: string;
  type_id: string;
  categories: string;
  tags: string;
  isCover: boolean;
  images: FileList;
  video_file?: FileList;
  specifications: {
    items: SpecificationItem[];
  };
  applications: {
    items: { value: string }[];
    images: FileList;
  };
  key_benefits: {
    items: { value: string }[];
    images: FileList;
  };
  // Optional properties for existing files in edit mode
  existingImages?: MediaItem[];
  existingVideo?: string;
  existingApplicationImages?: MediaItem[];
  existingKeyBenefitImages?: MediaItem[];
}

// Type (Category)
export interface SubType {
  id: string;
  name: string;
}

export interface Type {
  id: string;
  name: string;
  subTypes: SubType[];
}
