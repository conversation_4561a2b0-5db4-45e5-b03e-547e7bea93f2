import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useProductsApi } from '../../services/api/use-product-api';
import { useTypeApi } from '../../services/api/use-type-api';
import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowLeft, faEdit, faTag, faImage } from '@fortawesome/free-solid-svg-icons';
import { TabButton } from '../../components/common/TabButton';

const ProductDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const { useGetProduct } = useProductsApi();
  const { useGetTypes } = useTypeApi();
  const [activeTab, setActiveTab] = useState('Overview');

  // Fetch product data
  const { data: productResponse, isLoading: isLoadingProduct, error } = useGetProduct(
    id ? parseInt(id) : 0
  );
  
  // Extract product from the response
  const product = (productResponse as any)?.data ;

  // Fetch types for type name resolution
  const { data: typesResponse } = useGetTypes();
  const types = (typesResponse as any)?.Types;

  const getTypeName = (typeId: number | string | undefined): string => {
    if (!typeId) return 'N/A';
    return types?.find((t:any) => t.id === typeId.toString())?.name ?? 'N/A';
  };

  if (isLoadingProduct) return <div className="p-8 text-center">Loading product details...</div>;
  if (error) return <div className="p-8 text-center text-red-500">Error: {error.message}</div>;
  if (!product) return <div className="p-8 text-center">Product not found</div>;

  // Define available tabs based on product data
  const tabs = ['Overview'];
  
  // Only add tabs that have content
  if (product?.specifications && product.specifications.length > 0) {
    tabs.push('Specifications');
  }
  
  if (product?.applications && product.applications.length > 0) {
    tabs.push('Applications');
  }
  
  if (product?.key_benefits && product.key_benefits.length > 0) {
    tabs.push('Key Benefits');
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back button and actions */}
      <div className="flex justify-between items-center mb-6">
        <Link to="/dashboard/products" className="text-primary-01 hover:underline flex items-center">
          <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
          Back to Products
        </Link>
        <Link 
          to={`/dashboard/products/edit/${product.id}`}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center"
        >
          <FontAwesomeIcon icon={faEdit} className="mr-2" />
          Edit Product
        </Link>
      </div>

      {/* Product header */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="p-6">
          <h1 className="text-3xl font-bold text-secondary-01 mb-2">{product?.name}</h1>
          <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-4">
            <div>
              <span className="font-semibold">Type:</span> {product?.type ? product.type.name : 'N/A'}
            </div>
            {product?.categories && (
              <div>
                <span className="font-semibold">Category:</span>
                <span className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                  {product.categories}
                </span>
              </div>
            )}
            {product?.tags && (
              <div>
                <span className="font-semibold">Tags:</span>
                <span className="ml-2 bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                  <FontAwesomeIcon icon={faTag} className="mr-1" />
                  {product.tags}
                </span>
              </div>
            )}
            {product?.images && (
              <div>
                <span className="font-semibold">Images:</span>
                <span className="ml-2 flex items-center">
                  <FontAwesomeIcon icon={faImage} className="mr-1 text-blue-500" />
                  {product.images.length} images
                </span>
              </div>
            )}
          </div>
          <p className="text-gray-700">{product?.mini_description}</p>
        </div>
      </div>

      {/* Tabs navigation */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div className="border-b border-gray-200">
          <nav className="flex flex-wrap -mb-px">
            {tabs.map((tab) => (
              <TabButton
                key={tab}
                label={tab}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
              />
            ))}
          </nav>
        </div>

        {/* Tab content */}
        <div className="p-6">
          {activeTab === 'Overview' && (
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold mb-3 text-secondary-01">Description</h2>
                <p className="text-gray-700 whitespace-pre-line">{product?.description}</p>
              </div>
              
              {product?.images && product.images.length > 0 && (
                <div>
                  <h2 className="text-xl font-semibold mb-3 text-secondary-01">Product Images</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {product.images.map((img:any, idx:any) => (
                      <div key={idx} className="relative group">
                        <img
                          src={img.url}
                          alt={`${product.name} - ${idx + 1}`}
                          className="w-full h-64 object-cover rounded-lg shadow-md transition-transform duration-300 group-hover:scale-105"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {product?.video && (
                <div>
                  <h2 className="text-xl font-semibold mb-3 text-secondary-01">Product Video</h2>
                  <video
                    controls
                    className="w-full max-w-2xl rounded-lg shadow-md"
                    src={product.video}
                  />
                </div>
              )}
            </div>
          )}

          {activeTab === 'Specifications' && product?.specifications && product.specifications.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4 text-secondary-01">Product Specifications</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {product.specifications.map((spec:any, index:any) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-secondary-01">{spec.title}</h3>
                    <p className="text-gray-700">{spec.content}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'Applications' && product?.applications && product.applications.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4 text-secondary-01">Applications</h2>
              <div className="space-y-6">
                {product.applications.map((app:any, index:any) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-secondary-01 mb-2">{app.title}</h3>
                    {app.media && app.media.length > 0 && (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-3">
                        {app.media.map((img:any, idx:any) => (
                          <img
                            key={idx}
                            src={img.url}
                            alt={`${app.title} - ${idx + 1}`}
                            className="w-full h-48 object-cover rounded-lg shadow-md"
                          />
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'Key Benefits' && product?.key_benefits && product.key_benefits.length > 0 && (
            <div>
              <h2 className="text-xl font-semibold mb-4 text-secondary-01">Key Benefits</h2>
              <div className="space-y-6">
                {product.key_benefits.map((benefit:any, index:any) => (
                  <div key={index} className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-secondary-01 mb-2">{benefit.title}</h3>
                    {benefit.media && (
                      <div className="mt-3">
                        <img
                          src={benefit.media.url}
                          alt={benefit.title}
                          className="w-full max-h-64 object-contain rounded-lg shadow-md"
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsPage;