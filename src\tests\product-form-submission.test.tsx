import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { useProductForm } from '../sections/product/form/use-product-form';
import { useProductsApi } from '../services/api/use-product-api';
import { useTypeApi } from '../services/api/use-type-api';

// Mock the API hooks
vi.mock('../services/api/use-product-api');
vi.mock('../services/api/use-type-api');
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ id: undefined }),
    useNavigate: () => vi.fn(),
  };
});

const mockUseProductsApi = vi.mocked(useProductsApi);
const mockUseTypeApi = vi.mocked(useTypeApi);

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Product Form Submission', () => {
  let mockCreateProduct: ReturnType<typeof vi.fn>;
  let mockUpdateProduct: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();

    mockCreateProduct = vi.fn();
    mockUpdateProduct = vi.fn();

    // Mock API responses
    mockUseProductsApi.mockReturnValue({
      useGetProducts: () => ({ data: null, isLoading: false, error: null }),
      useGetProduct: () => ({ data: null, isLoading: false, error: null }),
      useCreateProduct: () => ({ mutate: mockCreateProduct, isPending: false }),
      useUpdateProduct: () => ({ mutate: mockUpdateProduct, isPending: false }),
      useDeleteProduct: () => ({ mutate: vi.fn(), isPending: false }),
    });

    mockUseTypeApi.mockReturnValue({
      useGetTypes: () => ({
        data: { data: [{ id: '1', name: 'Test Type', subTypes: [] }] },
        isLoading: false,
        error: null,
      }),
      useGetType: () => ({ data: null, isLoading: false, error: null }),
      useCreateType: () => ({ mutate: vi.fn(), isPending: false }),
      useUpdateType: () => ({ mutate: vi.fn(), isPending: false }),
      useDeleteType: () => ({ mutate: vi.fn(), isPending: false }),
    });
  });

  it('should handle create product form submission', async () => {
    const wrapper = createWrapper();
    const { result } = renderHook(() => useProductForm(), { wrapper });

    // Mock form data
    const mockFormData = {
      name: 'Test Product',
      description: 'Test Description',
      mini_description: 'Test Mini Description',
      type_id: '1',
      categories: 'Test Category',
      tags: 'test, product',
      images: new DataTransfer().files,
      video_file: new DataTransfer().files,
      specifications: { items: [], images: new DataTransfer().files },
      applications: { items: [], images: new DataTransfer().files },
      key_benefits: { items: [], images: new DataTransfer().files },
    };

    // Submit form
    await act(async () => {
      result.current.onSubmit(mockFormData);
    });

    // Verify create product was called
    expect(mockCreateProduct).toHaveBeenCalledTimes(1);
    expect(mockUpdateProduct).not.toHaveBeenCalled();

    // Verify FormData was created correctly
    const formDataArg = mockCreateProduct.mock.calls[0][0];
    expect(formDataArg).toBeInstanceOf(FormData);
  });

  it('should handle form submission with files', async () => {
    const wrapper = createWrapper();
    const { result } = renderHook(() => useProductForm(), { wrapper });

    // Create mock files
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockFileList = {
      0: mockFile,
      length: 1,
      item: (index: number) => index === 0 ? mockFile : null,
      [Symbol.iterator]: function* () { yield mockFile; }
    } as FileList;

    const mockFormData = {
      name: 'Test Product',
      description: 'Test Description',
      mini_description: 'Test Mini Description',
      type_id: '1',
      categories: 'Test Category',
      tags: 'test, product',
      images: mockFileList,
      video_file: new DataTransfer().files,
      specifications: { 
        items: [{ title: 'Spec 1', content: 'Content 1' }], 
        images: new DataTransfer().files 
      },
      applications: { 
        items: [{ value: 'App 1' }], 
        images: new DataTransfer().files 
      },
      key_benefits: { 
        items: [{ value: 'Benefit 1' }], 
        images: new DataTransfer().files 
      },
    };

    await act(async () => {
      result.current.onSubmit(mockFormData);
    });

    expect(mockCreateProduct).toHaveBeenCalledTimes(1);
    
    const formDataArg = mockCreateProduct.mock.calls[0][0];
    expect(formDataArg).toBeInstanceOf(FormData);
  });

  it('should provide correct form state', () => {
    const wrapper = createWrapper();
    const { result } = renderHook(() => useProductForm(), { wrapper });

    expect(result.current.isEditMode).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isSubmitting).toBe(false);
    expect(result.current.types).toEqual([{ id: '1', name: 'Test Type', subTypes: [] }]);
    expect(result.current.formMethods).toBeDefined();
    expect(result.current.onSubmit).toBeInstanceOf(Function);
  });
});
