import useApiServices from "../hooks/use-api-services";

import axiosInstance from "../../utils/axios";
// Define the API endpoints for Types
export const TypeEndpoints = {
  list: "/types",
  details: "/types",
  // selectTypes: "/allTypes",
};

interface Media {
  id: number;
  url: string;
  type: string;
}

interface ProductType {
  id: number;
  name: string;
}

interface Application {
  id: number;
  title: string;
  media: Media[];
}

interface KeyBenefit {
  id: number;
  title: string;
  media: Media | null;
}

interface Specification {
  id: number;
  title: string;
  content: string;
}

interface Product {
  id: number;
  type: ProductType;
  name: string;
  slug: string;
  is_home: boolean;
  description: string;
  mini_description: string;
  categories: string;
  tags: string;
  images: Media[];
  applications: Application[];
  key_benefits: KeyBenefit[];
  specifications: Specification[];
  video: string | null;
}

interface ProductCategory {
  name: string;
  parent_id: number;
  description: string;
  media: Media[];
  products: Product[];
}

interface Pagination {
  total: number;
  count: number;
  per_page: number;
  current_page: number;
  total_pages: number;
}

export interface TypeResponse {
  status: string;
  message: string;
  data: ProductCategory[];
  pagination: Pagination;
}
export type FormCreateUpdateData = {
  name: string;
  description: string;
  parent_id: string | number;
  image: File;
};

// Single type response for get by ID
export interface SingleTypeResponse {
  status: string;
  message: string;
  data: {
    id: number;
    name: string;
    description: string;
    parent_id: number | null;
    image: string;
    media: Media[];
    products: Product[];
  };
}

// Create a hook to use the Types API
export const useTypeApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Types with pagination
  const useGetTypes = (page: number = 1, per_page: number = 10) => {
    return apiServices.useGetListService<TypeResponse | any>({
      url: TypeEndpoints.list,
      params: {
        page: page.toString(),
        per_page: per_page.toString(),
      },
    });
  };
  const useGetSubTypes = (page: number = 1, per_page: number = 10) => {
    return apiServices.useGetListService<TypeResponse>({
      url: TypeEndpoints.list,

      params: {
        page: page.toString(),
        per_page: per_page.toString(),
        filter: "sub",
      },
    });
  };
  // Get a single Type by ID
  const useGetType = (id: number) => {
    return apiServices.useGetItemService<SingleTypeResponse>({
      url: TypeEndpoints.details,
      id: id.toString(),
      queryOptions: {
        enabled: id > 0, // Only fetch if we have a valid ID
      },
    });
  };

  // Create a new Type
  const useCreateType = (onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<FormData, any>({
      url: TypeEndpoints.list,
      onSuccess,
      withFormData: false,
    });
  };

  // Update a Type using PATCH
  const useUpdateType = (id: number, onSuccess?: () => void) => {
    return apiServices.usePostService<FormData, any>({
      url: `${TypeEndpoints.details}/${id.toString()}`,
      // id: ,
      onSuccess,
      withFormData: false,
      queryKey: TypeEndpoints.list + "list",
    });
  };

  // Delete a Type
  const useDeleteType = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: TypeEndpoints.details,
      urlAfterSuccess: TypeEndpoints.list + "list",
      onSuccess,
    });
  };

  return {
    useGetSubTypes,
    useGetTypes,
    useGetType,
    useCreateType,
    useUpdateType,
    useDeleteType,
  };
};
