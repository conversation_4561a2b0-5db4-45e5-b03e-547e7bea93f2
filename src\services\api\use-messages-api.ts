import React from "react";
import useApiServices from "../hooks/use-api-services";
import axiosInstance from "../../utils/axios";
// Define the API endpoints for Products
export const MessageEndpoints = {
  list: "/user-messages",
  details: "/user-messages",
};

type ContactMessage = {
  id: number;
  name: string;
  phone: string;
  email: string;
  message: string;
  finding_way: string;
};

type Pagination = {
  total: number;
  count: number;
  per_page: number;
  current_page: number;
  total_pages: number;
};

export type ContactMessagesResponse = {
  status: string;
  message: string;
  data: ContactMessage[];
  pagination: Pagination;
};

const useMessagesApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Products with pagination
  const useGetMessages = (page: number, per_page: number) => {
    return apiServices.useGetListService<ContactMessagesResponse>({
      url: MessageEndpoints.list,
      params: {
        page: page.toString(),
        per_page: per_page.toString(),
      },
    });
  };

  const useDeleteMessage = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<number>({
      url: MessageEndpoints.details,
      urlAfterSuccess: MessageEndpoints.list + "list",
      onSuccess,
    });
  };
  return {
    useGetMessages,
    useDeleteMessage,
  };
};
export default useMessagesApi;
