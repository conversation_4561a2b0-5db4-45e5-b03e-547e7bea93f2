import { useRef } from "react";
import { forwardRef } from "react";

import {
  closeSnackbar,
  SnackbarProvider as NotistackProvider,
} from "notistack";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faInfoCircle,
  faCheckCircle,
  faExclamationTriangle,
  faTimes,
  faTimesCircle,
} from "@fortawesome/free-solid-svg-icons";

type Props = {
  children: React.ReactNode;
};

export default function SnackbarProvider({ children }: Props) {
  const notistackRef = useRef<any>(null);

  // Tailwind CSS classes for different variants
  const variantStyles = {
    default: "bg-gray-100 text-gray-800",
    info: "bg-blue-100 text-blue-800",
    success: "bg-green-100 text-green-800",
    warning: "bg-yellow-100 text-yellow-800",
    error: "bg-red-100 text-red-800",
  };

  // Custom snackbar component using Tailwind

  // Custom snackbar component using Tailwind
  const CustomSnackbar = forwardRef<HTMLDivElement, any>(
    ({ id, message, variant }, ref) => (
      <div
        ref={ref}
        className={`flex items-center justify-between p-3 rounded shadow-md min-w-[300px] ${
          variantStyles[variant as keyof typeof variantStyles]
        }`}
      >
        <div className="flex items-center">{message}</div>
        <button
          onClick={() => closeSnackbar(id)}
          className="ml-4 text-gray-500 hover:text-gray-700"
        >
          <FontAwesomeIcon icon={faTimes} />
        </button>
      </div>
    )
  );

  CustomSnackbar.displayName = "CustomSnackbar";

  return (
    <NotistackProvider
      ref={notistackRef}
      maxSnack={10}
      preventDuplicate
      autoHideDuration={3000}
      TransitionComponent={undefined}
      variant="default"
      anchorOrigin={{ vertical: "top", horizontal: "right" }}
      iconVariant={{
        default: <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />,
        info: (
          <FontAwesomeIcon icon={faInfoCircle} className="mr-2 text-blue-500" />
        ),
        success: (
          <FontAwesomeIcon
            icon={faCheckCircle}
            className="mr-2 text-green-500"
          />
        ),
        warning: (
          <FontAwesomeIcon
            icon={faExclamationTriangle}
            className="mr-2 text-yellow-500"
          />
        ),
        error: (
          <FontAwesomeIcon icon={faTimesCircle} className="mr-2 text-red-500" />
        ),
      }}
      Components={{
        default: CustomSnackbar,
        info: CustomSnackbar,
        success: CustomSnackbar,
        warning: CustomSnackbar,
        error: CustomSnackbar,
      }}
      action={(snackbarId) => (
        <button
          onClick={() => closeSnackbar(snackbarId)}
          className="p-1 hover:bg-gray-200 rounded"
        >
          <FontAwesomeIcon icon={faTimes} className="w-4 h-4" />
        </button>
      )}
    >
      {children}
    </NotistackProvider>
  );
}
