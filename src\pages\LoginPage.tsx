// مع محاكاة
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../utils/axios";
import LoadingScreen from "../components/loading-screen/LoadingScreen";
// import api from '../api/axios';

const LoginPage = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      // Replace with your actual login API endpoint
      const response = await axiosInstance.post("/login", { email, password });
      const { token } = response.data;

      // Save the token and navigate to the dashboard
      localStorage.setItem("authToken", token);
      setLoading(false);
      navigate("/dashboard");
    } catch (err) {
      setLoading(false);
      setError("Invalid email or password.");
      console.error("Login failed:", err);
    }
  };
  return (
    <div className="flex items-center justify-center h-screen bg-gray-100">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-center text-primary-01">
          Admin Login
        </h1>
        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-secondary-01"
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01"
              required
            />
          </div>
          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-secondary-01"
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 mt-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-01 focus:border-primary-01"
              required
            />
          </div>
          {error && <p className="text-sm text-red-600">{error}</p>}
          <>
            {loading ? (
              <LoadingScreen />
            ) : (
              <button
                type="submit"
                className="w-full py-2 px-4 font-semibold text-white bg-primary-01/90 rounded-md hover:bg-primary-01 
            focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-01 cursor-pointer"
              >
                Login
              </button>
            )}
          </>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
