import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useParams, useNavigate } from "react-router-dom";
import type { TypeFormData } from "../types/types";
import { useTypeApi } from "../../../services/api/use-type-api";

// Maps the API type structure to the form's data structure
const typeToDefaultValues = (apiResponse: any): Partial<TypeFormData> => {
  console.log("Converting API response to form values:", apiResponse);

  // Handle the nested response structure
  const typeData = apiResponse?.data || apiResponse;

  return {
    name: typeData.name || "",
    description: typeData.description || "",
    parent_id: typeData.parent_id?.toString() || "",
    image: new DataTransfer().files, // Reset file input for edit mode
    existingImage: typeData.image || typeData.media?.[0]?.url || "",
  };
};

export const useTypeForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  // Initialize API hooks
  const { useGetTypes, useGetType, useCreateType, useUpdateType } =
    useTypeApi();

  // Queries
  const { data: typesResponse, isLoading: isLoadingTypes } = useGetTypes();
  console.log("typesResponse", typesResponse);

  // Get parent types for dropdown (all types can be parents)
  const parentTypes =
    typesResponse?.data?.map((type: any) => ({
      id: type?.id?.toString() || "",
      name: type.name,
    })) || [];

  const { data: typeToEdit, isLoading: isLoadingType } = useGetType(
    isEditMode ? parseInt(id!) : 0
  );

  // Mutations with success and error handlers
  const {
    mutate: addType,
    isPending: isAdding,
    error: createError,
  } = useCreateType(() => {
    navigate("/dashboard/types");
  });

  const {
    mutate: updateType,
    isPending: isUpdating,
    error: updateError,
  } = useUpdateType(isEditMode ? parseInt(id!) : 0);

  // Form setup
  const formMethods = useForm<TypeFormData>({
    defaultValues: {
      name: "",
      description: "",
      parent_id: "",
      image: new DataTransfer().files,
    },
    mode: "onChange", // Enable real-time validation
  });

  // State to track if form has been initialized to prevent constant resets
  const [formInitialized, setFormInitialized] = useState(false);

  // Reset form only once when type data is first loaded
  useEffect(() => {
    if (isEditMode && typeToEdit && !formInitialized) {
      console.log("Raw type data from API:", typeToEdit);
      const defaultValues = typeToDefaultValues(typeToEdit);
      console.log("Converted form values:", defaultValues);

      // Reset the form with the converted values
      formMethods.reset(defaultValues);
      setFormInitialized(true);
    } else if (!isEditMode && !formInitialized) {
      // Reset to empty form for create mode
      formMethods.reset({
        name: "",
        description: "",
        parent_id: "",
        image: new DataTransfer().files,
      });

      setFormInitialized(true);
    }
  }, [typeToEdit, isEditMode, formInitialized]);

  // Function to manually reset form
  const resetForm = () => {
    formMethods.reset({
      name: "",
      description: "",
      parent_id: "",
      image: new DataTransfer().files,
    });
  };

  // Create type submission logic
  const handleCreateType = (data: TypeFormData) => {
    const formData = new FormData();

    // Basic fields
    formData.append("name", data.name || "");
    formData.append("description", data.description || "");
    formData.append("parent_id", data.parent_id || "");

    // Image file
    if (data.image && data.image.length > 0) {
      formData.append("image", data.image[0]);
    }

    console.log("Sending FormData for create:", formData);
    addType(formData);
  };

  // Update type submission logic
  const handleUpdateType = (data: TypeFormData) => {
    const formData = new FormData();

    // Basic fields
    formData.append("name", data.name || "");
    formData.append("description", data.description || "");
    formData.append("parent_id", data.parent_id || "");
    formData.append("_method", "patch");

    // Image file (only append if new file is selected)
    if (data.image && data.image.length > 0) {
      formData.append("image", data.image[0]);
    }

    console.log("Sending FormData for update:", formData);
    updateType(formData, {
      onSuccess: () => {
        navigate("/dashboard/types");
      },
    });
  };

  // Main form submission handler
  const onSubmit = (data: TypeFormData) => {
    // Debug: Log form data to help troubleshoot
    console.log("Form submission data:", data);
    console.log("Form errors:", formMethods.formState.errors);

    // Additional validation check for required fields
    if (!data.name || data.name.trim() === "") {
      formMethods.setError("name", {
        message: "Type name is required",
      });
      return;
    }

    if (isEditMode) {
      handleUpdateType(data);
    } else {
      handleCreateType(data);
    }
  };

  // Extract existing image data for preview in edit mode
  const existingImage =
    isEditMode && typeToEdit
      ? typeToEdit.data?.image || typeToEdit.data?.media?.[0]?.url || ""
      : "";

  return {
    formMethods,
    isEditMode,
    isLoading: isLoadingTypes || isLoadingType,
    isSubmitting: isAdding || isUpdating,
    parentTypes,
    onSubmit,
    resetForm,
    existingImage,
    createError,
    updateError,
  };
};
