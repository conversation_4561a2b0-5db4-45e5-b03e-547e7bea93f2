import React, { useEffect, useState } from "react";
import { useForm, useFieldArray, useWatch } from "react-hook-form";
import { useQuery } from "@tanstack/react-query";
import { useParams, useNavigate } from "react-router-dom";
// Removed unused Product and Type imports since we're using any for API response
import type { ProductFormData } from "../types/types";
import { useProductsApi } from "../../../services/api/use-product-api";
import { useTypeApi } from "../../../services/api/use-type-api";

// Maps the API product structure to the form's data structure
const productToDefaultValues = (apiResponse: any): Partial<ProductFormData> => {
  console.log("Converting API response to form values:", apiResponse);

  // Handle the nested response structure
  const product = apiResponse?.data || apiResponse;

  return {
    name: product.name || "",
    description: product.description || "",
    mini_description: product.mini_description || "",
    type_id:
      product.sub_type?.id?.toString() ||
      product.sub_type_id?.toString() ||
      product.type?.id?.toString() ||
      product.type_id?.toString() ||
      "", // Handle nested sub_type object first, then fallback to type
    categories: product.categories || "",
    tags: product.tags || "",
    images: new DataTransfer().files, // Reset file inputs for edit mode
    video_file: new DataTransfer().files,

    specifications: {
      items:
        product.specifications?.map((spec: any) => ({
          title: spec.title || "",
          content: spec.content || "",
        })) || [],
    },
    applications: {
      items:
        product.applications?.map((app: any) => ({
          value: app.title || "",
        })) || [],
      images: new DataTransfer().files,
    },
    key_benefits: {
      items:
        product.key_benefits?.map((benefit: any) => ({
          value: benefit.title || "",
        })) || [],
      images: new DataTransfer().files,
    },

    // Store existing files for preview (not part of form data but useful for display)
    existingImages: product.images || [],
    existingVideo: product.video || "",
    existingApplicationImages:
      product.applications?.flatMap((app: any) => app.media || []) || [],
    existingKeyBenefitImages:
      product.key_benefits
        ?.map((benefit: any) => benefit.media)
        .filter(Boolean) || [],
  };
};

export const useProductForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  // Initialize API hooks
  const { useGetSubTypes } = useTypeApi();
  const { useGetProduct, useCreateProduct, useUpdateProduct } =
    useProductsApi();

  // Queries
  const { data: typesResponse, isLoading: isLoadingTypes } = useGetSubTypes();
  // Map MainType[] to Type[] format expected by the form
  const types = typesResponse?.data?.map((item: any) => ({
    id: item?.id?.toString() || "",
    name: item.name,
  }));

  const { data: productToEdit, isLoading: isLoadingProduct } = useGetProduct(
    isEditMode ? parseInt(id!) : 0
  );

  console.log("productToEdit", productToEdit);
  // Mutations with success and error handlers
  const {
    mutate: addProduct,
    isPending: isAdding,
    error: createError,
  } = useCreateProduct((data) => {
    navigate("/dashboard/products");
  });

  const {
    mutate: updateProduct,
    isPending: isUpdating,
    error: updateError,
  } = useUpdateProduct(isEditMode ? parseInt(id!) : 0);

  // Handle API validation errors
  const handleApiErrors = (error: any) => {
    if (
      error?.response?.data?.data &&
      Array.isArray(error.response.data.data)
    ) {
      // Handle validation errors from API
      const validationErrors = error.response.data.data;

      // Map API errors to form fields
      validationErrors.forEach((errorMessage: string) => {
        if (
          errorMessage.includes("نوع المنتج") ||
          errorMessage.includes("type")
        ) {
          formMethods.setError("type_id", { message: errorMessage });
        } else if (
          errorMessage.includes("اسم المنتج") ||
          errorMessage.includes("name")
        ) {
          formMethods.setError("name", { message: errorMessage });
        } else if (
          errorMessage.includes("الوصف الكامل") ||
          errorMessage.includes("description")
        ) {
          formMethods.setError("description", { message: errorMessage });
        } else if (
          errorMessage.includes("الوصف المختصر") ||
          errorMessage.includes("mini_description")
        ) {
          formMethods.setError("mini_description", { message: errorMessage });
        } else if (errorMessage.includes("categories")) {
          formMethods.setError("categories", { message: errorMessage });
        } else if (errorMessage.includes("tags")) {
          formMethods.setError("tags", { message: errorMessage });
        }
      });
    }
  };

  // Watch for API errors and handle them
  React.useEffect(() => {
    if (createError) {
      handleApiErrors(createError);
    }
  }, [createError]);

  React.useEffect(() => {
    if (updateError) {
      handleApiErrors(updateError);
    }
  }, [updateError]);

  // Set default values for the new nested structure with validation
  const formMethods = useForm<ProductFormData>({
    defaultValues: {
      name: "",
      description: "",
      mini_description: "",
      type_id: "",
      categories: "",
      tags: "",
      images: new DataTransfer().files,
      video_file: new DataTransfer().files,
      specifications: {
        items: [],
      },
      applications: { items: [], images: new DataTransfer().files },
      key_benefits: { items: [], images: new DataTransfer().files },
    },
    mode: "onBlur", // Validate on blur to avoid premature validation but still provide feedback
  });

  // Fetch all sub types (without parent filter)
  const { data: subTypesResponse, isLoading: isLoadingSubTypes } =
    useGetSubTypes();

  // Map sub types response to expected format
  const subTypes =
    subTypesResponse?.data?.map((subType: any) => ({
      id: subType?.id?.toString() || "",
      name: subType.name,
    })) || [];

  // Watch the selected type for any additional logic if needed
  const selectedTypeId = useWatch({
    control: formMethods.control,
    name: "type_id",
  });

  // Clear type_id error when a value is selected and trigger revalidation
  useEffect(() => {
    if (selectedTypeId && selectedTypeId !== "") {
      formMethods.clearErrors("type_id");
      // Trigger revalidation of the type_id field
      formMethods.trigger("type_id");
    }
  }, [selectedTypeId, formMethods]);

  // This useEffect will be moved after field array controls are declared

  // Function to manually reset form
  const resetForm = () => {
    formMethods.reset({
      name: "",
      description: "",
      mini_description: "",
      type_id: "",
      categories: "",
      tags: "",
      images: new DataTransfer().files,
      video_file: new DataTransfer().files,
      specifications: {
        items: [],
      },
      applications: { items: [], images: new DataTransfer().files },
      key_benefits: { items: [], images: new DataTransfer().files },
    });
  };

  const specControl = useFieldArray({
    control: formMethods.control,
    name: "specifications.items",
  });
  const appControl = useFieldArray({
    control: formMethods.control,
    name: "applications.items",
  });
  const benefitControl = useFieldArray({
    control: formMethods.control,
    name: "key_benefits.items",
  });

  // State to track if form has been initialized to prevent constant resets
  const [formInitialized, setFormInitialized] = useState(false);

  // Reset form only once when product data is first loaded
  useEffect(() => {
    if (isEditMode && productToEdit && !formInitialized) {
      console.log("Raw product data from API:", productToEdit);
      const defaultValues = productToDefaultValues(productToEdit);
      console.log("Converted form values:", defaultValues);

      // Reset the form with the converted values
      formMethods.reset(defaultValues);

      // Also reset the field arrays with the new data
      if (defaultValues.specifications?.items) {
        specControl.replace(defaultValues.specifications.items);
      }
      if (defaultValues.applications?.items) {
        appControl.replace(defaultValues.applications.items);
      }
      if (defaultValues.key_benefits?.items) {
        benefitControl.replace(defaultValues.key_benefits.items);
      }

      setFormInitialized(true);
    } else if (!isEditMode && !formInitialized) {
      // Reset to empty form for create mode
      formMethods.reset({
        name: "",
        description: "",
        mini_description: "",
        type_id: "",
        categories: "",
        tags: "",
        images: new DataTransfer().files,
        video_file: new DataTransfer().files,
        specifications: {
          items: [],
        },
        applications: { items: [], images: new DataTransfer().files },
        key_benefits: { items: [], images: new DataTransfer().files },
      });

      // Clear field arrays for create mode
      specControl.replace([]);
      appControl.replace([]);
      benefitControl.replace([]);

      setFormInitialized(true);
    }
  }, [productToEdit, isEditMode, formInitialized]);

  // Create product submission logic
  const handleCreateProduct = (data: ProductFormData) => {
    const formData = new FormData();

    // Basic fields - ensure they are not empty
    formData.append("name", data.name || "");
    formData.append("description", data.description || "");
    formData.append("mini_description", data.mini_description || "");
    formData.append("type_id", data.type_id || "");
    formData.append("categories", data.categories || "");
    formData.append("tags", data.tags || "");

    // Main images - append with [] notation for arrays
    if (data.images && data.images.length > 0) {
      Array.from(data.images).forEach((file) => {
        formData.append("images[]", file);
      });
    }

    // Video file
    if (data.video_file && data.video_file.length > 0) {
      formData.append("video_file", data.video_file[0]);
    }

    // Specifications - match the Postman format
    if (data.specifications.items.length > 0) {
      data.specifications.items.forEach((item, index) => {
        formData.append(`specifications[${index}][title]`, item.title || "");
        formData.append(
          `specifications[${index}][content]`,
          item.content || ""
        );
      });
    }

    // Applications - match the Postman format
    if (data.applications.items.length > 0) {
      data.applications.items.forEach((item, index) => {
        formData.append(`applications[${index}][title]`, item.value || "");
      });
    }

    // Application images - match the Postman format (separate from items)
    if (data.applications.images && data.applications.images.length > 0) {
      Array.from(data.applications.images).forEach((file) => {
        formData.append(`applications[0][images][]`, file);
      });
    }

    // Key Benefits - match the Postman format
    if (data.key_benefits.items.length > 0) {
      data.key_benefits.items.forEach((item, index) => {
        formData.append(`key_benefits[${index}][title]`, item.value || "");
      });

      // Key benefit images - match the Postman format (single image per benefit)
      if (data.key_benefits.images && data.key_benefits.images.length > 0) {
        Array.from(data.key_benefits.images).forEach((file, fileIndex) => {
          formData.append(`key_benefits[${fileIndex}][image]`, file);
        });
      }
    }

    console.log("Sending FormData for create:", formData);
    addProduct(formData);
  };

  // Update product submission logic
  const handleUpdateProduct = (data: ProductFormData) => {
    const formData = new FormData();
    formData.append("_method", "patch");
    // Basic fields - ensure they are not empty
    formData.append("name", data.name || "");
    formData.append("description", data.description || "");
    formData.append("mini_description", data.mini_description || "");
    formData.append("type_id", data.type_id || "");
    formData.append("categories", data.categories || "");
    formData.append("tags", data.tags || "");

    // Main images (only append if new files are selected)
    if (data.images && data.images.length > 0) {
      Array.from(data.images).forEach((file) => {
        formData.append("images[]", file);
      });
    }

    // Video file (only append if new file is selected)
    if (data.video_file && data.video_file.length > 0) {
      formData.append("video_file", data.video_file[0]);
    }

    // Specifications
    if (data.specifications.items.length > 0) {
      data.specifications.items.forEach((item, index) => {
        formData.append(`specifications[${index}][title]`, item.title || "");
        formData.append(
          `specifications[${index}][content]`,
          item.content || ""
        );
      });
    }

    // Applications
    if (data.applications.items.length > 0) {
      data.applications.items.forEach((item, index) => {
        formData.append(`applications[${index}][title]`, item.value || "");
      });
    }

    // Application images (only append if new files are selected)
    if (data.applications.images && data.applications.images.length > 0) {
      Array.from(data.applications.images).forEach((file) => {
        formData.append(`applications[0][images][]`, file);
      });
    }

    // Key Benefits
    if (data.key_benefits.items.length > 0) {
      data.key_benefits.items.forEach((item, index) => {
        formData.append(`key_benefits[${index}][title]`, item.value || "");
      });

      // Key benefit images (only append if new files are selected)
      if (data.key_benefits.images && data.key_benefits.images.length > 0) {
        Array.from(data.key_benefits.images).forEach((file, fileIndex) => {
          formData.append(`key_benefits[${fileIndex}][image]`, file);
        });
      }
    }

    console.log("Sending FormData for update:", formData);
    updateProduct(formData, {
      onSuccess: () => {
        navigate(-1);
      },
    });
  };

  // Main form submission handler
  const onSubmit = (data: ProductFormData) => {
    console.log("Form submission data:", data);
    console.log("Type ID value:", data.type_id);

    if (isEditMode) {
      handleUpdateProduct(data);
    } else {
      handleCreateProduct(data);
    }
  };

  // Extract existing files data for preview in edit mode
  const existingFiles =
    isEditMode && productToEdit
      ? {
          existingImages: productToEdit.data?.images || [],
          existingVideo: productToEdit.data?.video || "",
          existingApplicationImages:
            productToEdit.data?.applications?.flatMap(
              (app: any) => app.media || []
            ) || [],
          existingKeyBenefitImages:
            productToEdit.data?.key_benefits?.flatMap(
              (benefit: any) => benefit.media || []
            ) || [],
          existingSpecificationImages:
            productToEdit.data?.specifications?.flatMap(
              (spec: any) => spec.media || []
            ) || [],
        }
      : {};

  return {
    productToEdit,
    formMethods,
    isEditMode,
    isLoading: isLoadingTypes || isLoadingProduct || isLoadingSubTypes,
    isSubmitting: isAdding || isUpdating,
    types,
    subTypes,
    onSubmit,
    resetForm,
    specControl,
    appControl,
    benefitControl,
    ...existingFiles,
  };
};
