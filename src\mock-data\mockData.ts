import type { Type, SubType} from '../sections/type/types/types';
import type {Product} from '../sections/product/types/types';
import type { Portfolio } from '../sections/portfolio/types/types';
import type {UserMessage} from '../sections/messages/types/types'


export const mockTypes: Type[] = [
  {
    id: 'type-1',
    name: 'Conveyor Systems',
    subTypes: [
      { id: 'sub-type-1a', name: 'Roller Conveyors' },
      { id: 'sub-type-1b', name: 'Belt Conveyors' },
    ],
  },
  {
    id: 'type-2',
    name: '<PERSON><PERSON><PERSON> Handling',
    subTypes: [
      { id: 'sub-type-2a', name: '<PERSON><PERSON><PERSON> Lifts' },
      { id: 'sub-type-2b', name: 'Turntables' },
    ],
  },
];

// Mock Products
export const mockProducts: Product[] = [
  {
    id: 'prod-1',
    name: 'Advanced Belt Conveyor',
    description: 'A full description of the advanced belt conveyor, highlighting its features, performance, and integration capabilities.',
    mini_description: 'High-speed conveyor for modern logistics.',
    type_id: 'type-1',
    categories: 'Conveyors, Automation',
    tags: 'high-speed, logistics, sorting',
    isCover: true,
    images: ['https://placehold.co/600x400/f59e0b/white?text=Product+Image+1'],
    video_file: 'https://placehold.co/600x400/3b82f6/white?text=Product+Video',
    specifications: {
      items: [
        { title: 'Speed', content: 'Up to 3 m/s' },
        { title: 'Load Capacity', content: '50 kg/m' },
      ],
      images: [
        'https://placehold.co/400x300/78716c/white?text=Spec+Image+1'
      ]
    },
    applications: {
      items: [
        'E-commerce Sorting',
        'Warehouse Automation',
        'Assembly Lines'
      ],
      images: [
        'https://placehold.co/400x300/10b981/white?text=App+Image+1',
        'https://placehold.co/400x300/10b981/white?text=App+Image+2'
      ]
    },
    key_benefits: {
      items: [
        'High Efficiency',
        'Low Noise',
        'Modular Design'
      ],
      images: [
        'https://placehold.co/400x300/ef4444/white?text=Benefit+Image+1'
      ]
    },
  },
  {
    id: 'prod-2',
    name: 'Robotic Palletizer',
    description: 'Automated robotic palletizer for end-of-line packaging. Increases efficiency and reduces labor costs.',
    mini_description: 'Automated solution for pallet stacking.',
    type_id: 'type-2',
    categories: 'Robotics, Palletizing',
    tags: 'automation, packaging',
    isCover: false,
    images: ['https://placehold.co/600x400/6366f1/white?text=Product+Image+2'],
    
    specifications: {
        items: [{ title: 'Payload', content: 'Up to 180 kg' }],
        images: [] 
    },
    applications: {
        items: [],
        images: []
    },
    key_benefits: {
        items: [],
        images: []
    },
  },
];


export const fetchProducts = async (): Promise<Product[]> => {
  console.log("Simulating API call for products...");
  await new Promise((resolve) => setTimeout(resolve, 300));
  return Promise.resolve(mockProducts);
};

export const fetchProductById = async (id: string): Promise<Product | undefined> => {
  console.log(`Simulating API call for product with ID: ${id}`);
  await new Promise((resolve) => setTimeout(resolve, 300));
  return Promise.resolve(mockProducts.find(p => p.id === id));
};



// Mock User Messages
export const mockMessages: UserMessage[] = [
  { id: 'msg-1', name: 'John Doe', email: '<EMAIL>', phone: '************', source: 'Website Form' },
  { id: 'msg-2', name: 'Jane Smith', email: '<EMAIL>', phone: '************', source: 'Social Media' },
  { id: 'msg-3', name: 'Sam Wilson', email: '<EMAIL>', phone: '************', source: 'Referral' },
];

// Mock Portfolio
export const mockPortfolios: Portfolio[] = [
    {
        id: 'port-1',
        name: 'Corporate Branding',
        items: [
            { text: 'First item text about corporate identity.', 
              images: [
                'https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg', 
                'https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg',
                'https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg',
              'https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg'] },
            { text: 'Second item text, focusing on logo design.', images: ['https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg'] },
            { text: 'Third item text about marketing materials.', images: ['https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg', 'https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg'] },
        ]
    },
    {
        id: 'port-2',
        name: 'Web Development Projects',
        items: [
            { text: 'E-commerce platform development.', images: ['https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg'] },
            { text: 'Portfolio website for a photographer.', images: ['https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg', 'https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg'] },
            { text: 'Custom booking system implementation.', images: ['https://i.pinimg.com/1200x/f5/3b/d2/f53bd2f7c1572fe49740f96cbc98bc53.jpg'] },
        ]
    }
];
export const fetchTypes = async (): Promise<Type[]> => {
  console.log('Simulating API call to fetch all types...');
  await new Promise((resolve) => setTimeout(resolve, 300));
  return Promise.resolve(mockTypes);
};

export const fetchSubTypeById = async (id: string): Promise<SubType | undefined> => {
  console.log(`Simulating fetch for sub-type ID: ${id}`);
  await new Promise((resolve) => setTimeout(resolve, 300));
  for (const type of mockTypes) {
    const found = type.subTypes?.find((sub) => sub.id === id);
    if (found) return found;
  }
  return undefined;
};