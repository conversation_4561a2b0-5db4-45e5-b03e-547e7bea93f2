# Product CRUD Usage Example

This document shows how to use the updated Product CRUD system with the proper schema and API integration.

## Schema Definition

The `CreateProductBody` type defines the structure for both create and update operations:

```typescript
export type CreateProductBody = {
  name: string;
  description: string;
  mini_description: string;
  type_id: string;
  categories: string;
  tags: string;
  images?: File[];
  video_file?: File;
  specifications?: {
    title: string;
    content: string;
  }[];
  applications?: {
    title: string;
    images?: File[];
  }[];
  key_benefits?: {
    title: string;
    images?: File[];
  }[];
};
```

## API Response Structure

The API returns data in this format:

```typescript
export interface ProductsListResponse {
  data: Product[];
  pagination: {
    total: number;
    count: number;
    per_page: number;
    current_page: number;
    total_pages: number;
  };
}
```

## How to Use

### 1. Create Product

Navigate to `/dashboard/products/add` or use the form directly:

```typescript
import { useProductForm } from '../../sections/product/form/use-product-form';
import { ProductForm } from '../../sections/product/form/product-form';
import { FormProvider } from 'react-hook-form';

const CreateProductPage = () => {
  const { formMethods, onSubmit, types, isSubmitting, isLoading } = useProductForm();

  if (isLoading) return <div>Loading...</div>;

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSubmit)}>
        <ProductForm types={types} />
        <button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Creating...' : 'Create Product'}
        </button>
      </form>
    </FormProvider>
  );
};
```

### 2. Edit Product

Navigate to `/dashboard/products/edit/:id`:

```typescript
// The same component works for both create and edit
// The hook automatically detects edit mode based on URL params
const EditProductPage = () => {
  const { formMethods, onSubmit, types, isSubmitting, isLoading, isEditMode } = useProductForm();

  if (isLoading) return <div>Loading product data...</div>;

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSubmit)}>
        <ProductForm types={types} />
        <button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Updating...' : 'Update Product'}
        </button>
      </form>
    </FormProvider>
  );
};
```

### 3. List Products

```typescript
import { useProductView } from '../../sections/product/view/use-product-view';
import { ProductView } from '../../sections/product/view/product-view';

const ProductListPage = () => {
  const productViewProps = useProductView();

  return <ProductView {...productViewProps} />;
};
```

### 4. Delete Product

The delete functionality is built into the ProductView component:

```typescript
// Delete is handled automatically in the ProductView component
// Users click the delete button, confirm in modal, and product is deleted
```

## Form Data Processing

The `useProductForm` hook handles all the complex logic:

### Create Product Flow

1. User fills out form
2. `handleCreateProduct` is called
3. Form data is converted to FormData
4. Files are properly appended
5. API call is made via `useCreateProduct`
6. On success, user is redirected to products list

### Update Product Flow

1. Existing product data is loaded and form is pre-populated
2. User modifies form
3. `handleUpdateProduct` is called
4. Only changed files are appended to FormData
5. API call is made via `useUpdateProduct`
6. On success, user is redirected to products list

## File Upload Handling

The system properly handles file uploads for:

- **Main product images** (`images: File[]`)
- **Video files** (`video_file: File`)
- **Application images** (`applications[].images: File[]`)
- **Key benefit images** (`key_benefits[].images: File[]`)

Files are automatically converted to FormData format for API submission.

## Error Handling

The system includes comprehensive error handling:

- Form validation errors are displayed inline
- API errors are shown via snackbar notifications
- Loading states prevent multiple submissions
- Network errors are handled gracefully

## Integration with Existing Systems

The CRUD system integrates seamlessly with:

- ✅ React Query for caching and state management
- ✅ React Hook Form for form management
- ✅ Existing API service architecture
- ✅ File upload handling
- ✅ Error notification system
- ✅ Routing and navigation
- ✅ Authentication and authorization

## Complete Example Usage

```typescript
// pages/product/product-management.tsx
import React from 'react';
import { Routes, Route } from 'react-router-dom';
import ProductFormPage from './product-form-page';
import ProductsCrudPage from './products-crud-page';

const ProductManagement = () => {
  return (
    <Routes>
      <Route index element={<ProductsCrudPage />} />
      <Route path="add" element={<ProductFormPage />} />
      <Route path="edit/:id" element={<ProductFormPage />} />
    </Routes>
  );
};

export default ProductManagement;
```

This setup provides a complete, production-ready CRUD system for products with proper file upload handling, error management, and seamless API integration.
