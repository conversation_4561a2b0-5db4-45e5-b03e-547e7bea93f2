import { useState, useCallback } from "react";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { useTypeApi } from "../../../services/api/use-type-api";

// Mock function for updating type cover status (if needed)
const updateTypeCoverStatus = async ({
  id,
  isCover,
}: {
  id: string;
  isCover: boolean;
}) => {
  console.log(
    `Simulating update for type ${id}: setting isCover to ${isCover}`
  );
  await new Promise((res) => setTimeout(res, 500));
  return { success: true };
};

export const useTypeView = () => {
  const queryClient = useQueryClient();
  const [expandedTypeId, setExpandedTypeId] = useState<string | null>(null);

  // State for Delete Modal
  const [isDeleteModalOpen, setDeleteModalOpen] = useState(false);
  const [typeForDelete, setTypeForDelete] = useState<any | undefined>(
    undefined
  );

  // State for "Show on Home" Cover Toggle Modal (if needed)
  const [isCoverModalOpen, setCoverModalOpen] = useState(false);
  const [typeForCover, setTypeForCover] = useState<any | undefined>(undefined);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Initialize API hooks
  const { useGetSubTypes, useDeleteType } = useTypeApi();

  // Queries with server-side pagination
  const {
    data: typesResponse,
    isLoading,
    error,
  } = useGetSubTypes(currentPage, itemsPerPage);
  const types = typesResponse?.data;
  const pagination = typesResponse?.pagination;

  // Server-side pagination info
  const totalItems = pagination?.total || 0;
  const totalPages = pagination?.total_pages || 1;

  // Mutations
  const { mutate: deleteType, isPending: isDeleting } = useDeleteType(() => {
    setDeleteModalOpen(false);
    setTypeForDelete(undefined);
  });

  const { mutate: updateCover, isPending: isUpdatingCover } = useMutation({
    mutationFn: updateTypeCoverStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["types"] });
      setCoverModalOpen(false);
      setTypeForCover(undefined);
    },
  });

  // Toggle expanded type
  const handleToggleExpand = (typeId: string) => {
    setExpandedTypeId(expandedTypeId === typeId ? null : typeId);
  };

  // Delete modal handlers
  const openDeleteModal = useCallback((type: any) => {
    setTypeForDelete(type);
    setDeleteModalOpen(true);
  }, []);

  const closeDeleteModal = useCallback(() => {
    setDeleteModalOpen(false);
    setTypeForDelete(undefined);
  }, []);

  const handleConfirmDelete = useCallback(() => {
    if (typeForDelete) {
      deleteType(typeForDelete.id);
    }
  }, [typeForDelete, deleteType]);

  // Cover modal handlers (if needed)
  const openCoverModal = useCallback((type: any) => {
    setTypeForCover(type);
    setCoverModalOpen(true);
  }, []);

  const closeCoverModal = useCallback(() => {
    setCoverModalOpen(false);
    setTypeForCover(undefined);
  }, []);

  const handleConfirmCoverToggle = useCallback(() => {
    if (typeForCover) {
      updateCover({
        id: typeForCover.id,
        isCover: !typeForCover.isCover,
      });
    }
  }, [typeForCover, updateCover]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // Close any expanded type when changing pages
      setExpandedTypeId(null);
    }
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    // Reset to first page when changing items per page
    setCurrentPage(1);
    // Close any expanded type
    setExpandedTypeId(null);
  };

  return {
    types,
    isLoading,
    error,
    expandedTypeId,
    handleToggleExpand,
    isDeleteModalOpen,
    isDeleting,
    selectedType: typeForDelete,
    openDeleteModal,
    closeDeleteModal,
    handleConfirmDelete,
    isCoverModalOpen,
    isUpdatingCover,
    typeForCover,
    openCoverModal,
    closeCoverModal,
    handleConfirmCoverToggle,
    // Pagination props
    currentPage,
    totalPages,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    totalItems,
    pagination,
  };
};
