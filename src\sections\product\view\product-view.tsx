import { Fragment } from "react";
import { Link } from "react-router-dom";
import type { Product } from "../../type/types/types";
import ConfirmationModal from "../../../components/common/ConfirmationModal";
import { ProductDetails } from "./product-details";
import ToggleSwitch from "../../../components/common/ToggleSwitch";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTag, faEye } from "@fortawesome/free-solid-svg-icons";
import Pagination from "../../../components/common/Pagination";
import { useProductView } from "./use-product-view";

export const ProductView = () => {
  const {
    products,
    isLoading,
    error,
    expandedProductId,
    handleToggleExpand,
    isDeleteModalOpen,
    isDeleting,
    selectedProduct,
    openDeleteModal,
    closeDeleteModal,
    handleConfirmDelete,
    isCoverModalOpen,
    isUpdatingCover,
    productForCover,
    openCoverModal,
    closeCoverModal,
    handleConfirmCoverToggle,
    currentPage,
    totalPages,
    itemsPerPage,
    handlePageChange,
    handleItemsPerPageChange,
    totalItems,
  } = useProductView();

  if (isLoading) return <div>Loading products...</div>;
  if (error) return <div>An error occurred: {error.message}</div>;

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-secondary-01">Products</h1>
          <p className="text-gray-600 text-sm mt-1">
            Total: {totalItems} products
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center">
            <label
              htmlFor="itemsPerPage"
              className="mr-2 text-sm font-medium text-gray-700"
            >
              Show:
            </label>
            <select
              id="itemsPerPage"
              value={itemsPerPage}
              onChange={(e) => handleItemsPerPageChange(Number(e.target.value))}
              className="border border-gray-300 rounded-md text-sm p-1"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
          </div>
          <Link
            to="/dashboard/products/add"
            className="px-4 py-2 font-semibold text-white bg-primary-01/90 rounded-md hover:bg-primary-01"
          >
            + Add Product
          </Link>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="w-full text-left">
          <thead className="border-b-2 border-gray-200 bg-gray-50">
            <tr>
              <th className="py-3 px-6 font-semibold text-primary-01">
                Product Name
              </th>
              <th className="py-3 px-6 font-semibold text-primary-01">Type</th>
              <th className="py-3 px-6 font-semibold text-primary-01">
                Categories
              </th>
              <th className="py-3 px-6 font-semibold text-primary-01">Tags</th>
              <th className="py-3 px-6 font-semibold text-primary-01">
                Images
              </th>
              <th className="py-3 px-6 font-semibold text-primary-01">
                Show on Home
              </th>
              <th className="py-3 px-6 font-semibold text-primary-01 w-40 text-center">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {products?.map((product) => (
              <Fragment key={product.id}>
                <tr className="border-b border-gray-200 hover:bg-yellow-100 transition-colors duration-200">
                  <td className="py-3 px-6 font-medium text-secondary-01">
                    {product.name}
                  </td>
                  <td className="py-3 px-6 text-sm text-gray-600">
                    {product.type?.name || "N/A"}
                  </td>
                  <td className="py-3 px-6">
                    {product.categories ? (
                      <div className="flex flex-wrap gap-1">
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                          {product.categories}
                        </span>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">None</span>
                    )}
                  </td>
                  <td className="py-3 px-6">
                    {product.tags ? (
                      <div className="flex flex-wrap gap-1">
                        <span className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                          <FontAwesomeIcon icon={faTag} className="mr-1" />
                          {product.tags}
                        </span>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">None</span>
                    )}
                  </td>
                  <td className="py-3 px-6">
                    {product.images && product.images.length > 0 ? (
                      <div className="flex items-center">
                        <FontAwesomeIcon
                          icon={faEye}
                          className="mr-1 text-blue-500"
                        />
                        <span>{product.images.length} images</span>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">No images</span>
                    )}
                  </td>
                  <td className="py-3 px-6">
                    <ToggleSwitch
                      checked={product.is_home!}
                      onChange={(e) => openCoverModal(e, product)}
                      label={
                        product.is_home
                          ? `Remove ${product.name} from homepage`
                          : `Add ${product.name} to homepage`
                      }
                    />
                  </td>
                  <td className="py-3 px-6">
                    <div className="flex items-center justify-center space-x-4">
                      <Link
                        to={`/dashboard/products/details/${product.id}`}
                        onClick={(e) => e.stopPropagation()}
                        className="text-green-600 hover:underline font-semibold cursor-pointer"
                      >
                        Details
                      </Link>
                      <Link
                        to={`/dashboard/products/edit/${product.id}`}
                        onClick={(e) => e.stopPropagation()}
                        className="text-blue-500 hover:underline font-semibold"
                      >
                        Edit
                      </Link>
                      <button
                        onClick={(e) => openDeleteModal(e, product)}
                        className="text-red-500 hover:underline font-semibold cursor-pointer"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>

                {expandedProductId === product.id && (
                  <tr className="bg-gray-50">
                    <td colSpan={7} className="p-4">
                      <ProductDetails product={product} />
                    </td>
                  </tr>
                )}
              </Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="mt-6 flex flex-col sm:flex-row justify-between items-center">
        <div className="text-sm text-gray-500 mb-2 sm:mb-0">
          Showing {products?.length || 0} of {totalItems} products
        </div>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          className="mt-2 sm:mt-0"
        />
      </div>

      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleConfirmDelete}
        isConfirming={isDeleting}
        title="Delete Product"
        message={`Are you sure you want to delete "${
          selectedProduct?.name ?? ""
        }"? This action cannot be undone.`}
      />

      <ConfirmationModal
        isOpen={isCoverModalOpen}
        onClose={closeCoverModal}
        onConfirm={handleConfirmCoverToggle}
        isConfirming={isUpdatingCover}
        title={
          productForCover?.is_home ? "Remove from Homepage" : "Add to Homepage"
        }
        message={
          productForCover?.is_home
            ? `Are you sure you want to remove "${
                productForCover?.name ?? ""
              }" from the homepage?`
            : `Are you sure you want to add "${
                productForCover?.name ?? ""
              }" to the homepage?`
        }
      />
    </div>
  );
};
