// ===================================================================
// Product-related Types (Based on API)
// ===================================================================

export interface SpecificationItem {
  title: string;
  content: string;
}

export interface ApplicationItem {
  title: string;
  images: (File | string)[]; // Supports uploaded files or URL strings
}

export interface KeyBenefitItem {
  title: string;
  image: File | string; // Supports an uploaded file or a URL string
}

// The definitive Product structure based on your API
export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  mini_description: string;
  type: {
    id: number;
    name: string;
  };
  type_id: string; // Keep for backward compatibility with forms
  categories: string; // Used for tags/grouping
  tags: string;
  is_home: boolean; // Whether the product is shown on homepage
  images: (File | string)[];
  video_file?: File | string;
  specifications: SpecificationItem[];
  applications: ApplicationItem[];
  key_benefits: KeyBenefitItem[];
}

// Structure for react-hook-form, handles FileList for uploads
export interface ProductFormData {
  name: string;
  slug: string;
  description: string;
  mini_description: string;
  type_id: string;
  categories: string;
  tags: string;
  images: FileList;
  video_file?: FileList;
  specifications: SpecificationItem[];
  applications: { title: string; images: FileList }[];
  key_benefits: { title: string; image: FileList }[];
}

// ===================================================================
// Type (Category)-related Types
// ===================================================================

// SubType does not need to contain full product objects, just IDs if needed.
// For simplicity, we'll keep it clean.
export interface SubType {
  id: string;
  name: string;
}

export interface Type {
  id: string;
  name: string;
  subTypes: SubType[];
}

export interface TypeFormData {
  name: string;
  description: string;
  parent_id: string;
  image: FileList;
  // For edit mode - existing image preview
  existingImage?: string;
}

// API response type for single type
export interface TypeData {
  id: number;
  name: string;
  description: string;
  parent_id: number | null;
  image: string;
  media: Array<{
    id: number;
    url: string;
    type: string;
  }>;
  products: Product[];
}

// ===================================================================
// Other Application Types
// ===================================================================

export interface UserMessage {
  id: string;
  name: string;
  email: string;
  phone: string;
  source: string;
}

export interface PortfolioItem {
  text: string;
  images: string[];
}

export interface Portfolio {
  id: string;
  name: string;
  items: PortfolioItem[];
}
